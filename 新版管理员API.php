<?php
/**
 * 管理员API v2.0 - 激活码系统管理
 * 支持激活码生成、点数管理、封禁系统、云数据管理等功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入依赖文件
require_once '数据库管理类.php';
require_once 'DES加密工具类.php';
require_once '激活码生成工具类.php';
require_once '配置文件.php';
require_once '配置文件.php';

class 新版管理员API {
    private $数据库;
    private $加密工具;
    private $激活码工具;
    private $管理员密码 = 'admin123456'; // 默认管理员密码
    
    public function __construct() {
        $this->数据库 = new 数据库管理类();
        $this->加密工具 = new DES加密工具类();
        $this->激活码工具 = new 激活码生成工具类();
    }
    
    /**
     * 处理管理员请求
     */
    public function 处理请求() {
        try {
            // 获取请求数据
            $输入数据 = file_get_contents('php://input');
            if (empty($输入数据)) {
                throw new Exception('请求数据为空');
            }
            
            $请求数据 = json_decode($输入数据, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON格式错误: ' . json_last_error_msg());
            }
            
            // 检查是否为加密请求
            if (isset($请求数据['encrypted_data'])) {
                $解密数据 = $this->加密工具->解析安全请求($请求数据['encrypted_data']);
            } else {
                $解密数据 = $请求数据;
            }
            
            // 验证管理员密码
            if (!isset($解密数据['admin_password']) || $解密数据['admin_password'] !== $this->管理员密码) {
                throw new Exception('管理员密码错误');
            }
            
            // 获取操作类型
            $操作 = $解密数据['action'] ?? '';
            
            // 路由到对应的处理方法
            switch ($操作) {
                case 'get_auth_codes':
                    $结果 = $this->获取激活码列表($解密数据);
                    break;
                case 'generate_auth_codes':
                    $结果 = $this->生成激活码($解密数据);
                    break;
                case 'import_auth_code':
                    $结果 = $this->导入激活码($解密数据);
                    break;
                case 'batch_import_auth_codes':
                    $结果 = $this->批量导入激活码($解密数据);
                    break;
                case 'modify_points':
                    $结果 = $this->修改点数($解密数据);
                    break;
                case 'ban_target':
                    $结果 = $this->封禁目标($解密数据);
                    break;
                case 'unban_target':
                    $结果 = $this->解除封禁($解密数据);
                    break;
                case 'get_ban_list':
                    $结果 = $this->获取封禁列表($解密数据);
                    break;
                case 'get_system_stats':
                    $结果 = $this->获取系统统计($解密数据);
                    break;
                case 'get_login_logs':
                    $结果 = $this->获取登录日志($解密数据);
                    break;
                case 'get_points_logs':
                    $结果 = $this->获取点数记录($解密数据);
                    break;
                case 'get_ip_stats':
                    $结果 = $this->获取IP地理位置统计($解密数据);
                    break;
                case 'manage_cloud_data':
                    $结果 = $this->管理云数据($解密数据);
                    break;
                case 'reset_unbind_count':
                    $结果 = $this->重置解绑次数($解密数据);
                    break;
                case 'admin_unbind_device':
                    $结果 = $this->管理员解绑设备($解密数据);
                    break;
                case 'get_auth_code_detail':
                    $结果 = $this->获取激活码详细信息($解密数据);
                    break;
                case 'delete_auth_code':
                    $结果 = $this->删除激活码($解密数据);
                    break;
                case 'batch_delete_auth_codes':
                    $结果 = $this->批量删除激活码($解密数据);
                    break;
                case 'modify_unbind_limit':
                    $结果 = $this->修改解绑次数限制($解密数据);
                    break;
                case 'set_cloud_data':
                    $结果 = $this->设置云数据($解密数据);
                    break;
                case 'get_system_config':
                    $结果 = $this->获取系统配置($解密数据);
                    break;
                case 'update_system_config':
                    $结果 = $this->更新系统配置($解密数据);
                    break;
                case 'get_logs':
                    $结果 = $this->获取日志列表($解密数据);
                    break;
                case 'get_log_stats':
                    $结果 = $this->获取日志统计($解密数据);
                    break;
                case 'export_logs':
                    $结果 = $this->导出日志($解密数据);
                    break;
                case 'cleanup_logs':
                    $结果 = $this->清理日志($解密数据);
                    break;
                default:
                    throw new Exception('未知的操作类型: ' . $操作);
            }
            
            // 返回成功响应
            $this->返回成功响应($结果);
            
        } catch (Exception $e) {
            $this->返回错误响应($e->getMessage());
        }
    }
    
    /**
     * 获取激活码列表
     */
    private function 获取激活码列表($数据) {
        try {
            $限制 = $数据['limit'] ?? 10000;  // 增加默认限制到10000
            $状态过滤 = $数据['status_filter'] ?? null;
            $包含云数据 = $数据['include_cloud_data'] ?? false;

            // 首先检查激活码表是否存在
            $表检查 = $this->数据库->查询("SHOW TABLES LIKE '激活码表'");
            if (empty($表检查)) {
                // 如果激活码表不存在，尝试从旧版用户验证码表获取数据
                return $this->获取旧版用户列表($限制, $状态过滤);
            }

            $激活码列表 = $this->数据库->获取激活码列表($限制, $状态过滤);

            // 为每个激活码添加最新登录位置信息和云数据
            foreach ($激活码列表 as &$激活码) {
                try {
                    $最新位置 = $this->数据库->获取激活码最新登录位置($激活码['激活码']);
                    if ($最新位置) {
                        $激活码['最新登录IP'] = $最新位置['登录IP'];
                        $激活码['最新登录位置'] = $this->格式化位置信息($最新位置);
                        $激活码['最新登录时间'] = $最新位置['登录时间'];
                    } else {
                        $激活码['最新登录IP'] = null;
                        $激活码['最新登录位置'] = '从未登录';
                        $激活码['最新登录时间'] = null;
                    }
                } catch (Exception $e) {
                    // 如果获取登录位置失败，设置默认值
                    $激活码['最新登录IP'] = null;
                    $激活码['最新登录位置'] = '位置获取失败';
                    $激活码['最新登录时间'] = null;
                }

                // 如果请求包含云数据，则获取云数据信息
                if ($包含云数据) {
                    try {
                        $云数据 = $this->数据库->获取云数据($激活码['激活码']);
                        $激活码['云数据'] = $云数据 ?: '';
                    } catch (Exception $e) {
                        $激活码['云数据'] = '';
                    }
                } else {
                    $激活码['云数据'] = '';
                }

                // 格式化状态
                $状态文本 = ['未使用', '已使用', '已封禁'];
                $激活码['状态文本'] = $状态文本[$激活码['激活码状态']] ?? '未知';
            }

            return [
                'status' => 'success',
                'message' => '获取激活码列表成功',
                'data' => $激活码列表,
                'total' => count($激活码列表)
            ];

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => '获取激活码列表失败: ' . $e->getMessage(),
                'data' => [],
                'count' => 0
            ];
        }
    }

    /**
     * 获取旧版用户列表（兼容性方法）
     */
    private function 获取旧版用户列表($限制, $状态过滤) {
        try {
            $sql = "SELECT `验证码` as `激活码`, `用户名`, `状态` as `激活码状态`, `绑定机器码`, `最后登录时间`, `备注` FROM `用户验证码表`";
            $参数 = [];

            if ($状态过滤 !== null) {
                $sql .= " WHERE `状态` = ?";
                $参数[] = $状态过滤;
            }

            $sql .= " ORDER BY `id` DESC LIMIT ?";
            $参数[] = $限制;

            $用户列表 = $this->数据库->查询($sql, $参数);

            // 格式化旧版数据
            foreach ($用户列表 as &$用户) {
                $用户['剩余点数'] = 0; // 旧版没有点数概念
                $用户['初始点数'] = 0;
                $用户['状态文本'] = $用户['激活码状态'] == 1 ? '已使用' : '未使用';
                $用户['最新登录位置'] = '旧版数据';
                $用户['最新登录时间'] = $用户['最后登录时间'];
                $用户['最新登录IP'] = null;
            }

            return [
                'status' => 'success',
                'message' => '获取用户列表成功（旧版兼容模式）',
                'data' => $用户列表,
                'count' => count($用户列表)
            ];

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => '获取用户列表失败: ' . $e->getMessage(),
                'data' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * 生成激活码
     */
    private function 生成激活码($数据) {
        $数量 = $数据['count'] ?? 1;
        $初始点数 = $数据['initial_points'] ?? 9999;
        $备注 = $数据['remark'] ?? '批量生成';

        if ($数量 <= 0 || $数量 > 1000) {
            throw new Exception('生成数量必须在1-1000之间');
        }

        if ($初始点数 <= 0) {
            throw new Exception('初始点数必须大于0');
        }

        // 获取已存在的激活码
        $已存在激活码 = array_column($this->数据库->查询("SELECT 激活码 FROM `激活码表`"), '激活码');

        // 生成激活码数据
        $激活码数据 = $this->激活码工具->生成激活码数据($数量, $初始点数, $备注, $已存在激活码);

        // 批量插入数据库
        $成功数量 = 0;
        $生成的激活码 = [];

        foreach ($激活码数据 as $数据项) {
            try {
                $sql = "INSERT INTO `激活码表` (`激活码`, `激活码状态`, `初始点数`, `剩余点数`, `每次扣除点数`, `最大解绑次数`, `备注`) VALUES (?, ?, ?, ?, ?, ?, ?)";

                $this->数据库->执行($sql, [
                    $数据项['激活码'],
                    $数据项['激活码状态'],
                    $数据项['初始点数'],
                    $数据项['剩余点数'],
                    $数据项['每次扣除点数'],
                    $数据项['最大解绑次数'],
                    $数据项['备注']
                ]);

                $生成的激活码[] = $数据项['激活码'];
                $成功数量++;

            } catch (Exception $e) {
                // 记录失败但继续处理其他激活码
                continue;
            }
        }
        
        if ($成功数量 === 0) {
            throw new Exception('激活码生成失败');
        }
        
        // 生成导出数据
        $导出数据 = [
            'txt' => $this->激活码工具->导出激活码($激活码数据, 'txt'),
            'csv' => $this->激活码工具->导出激活码($激活码数据, 'csv'),
            'json' => $this->激活码工具->导出激活码($激活码数据, 'json')
        ];
        
        return [
            'status' => 'success',
            'message' => "成功生成 {$成功数量} 个激活码",
            'generated_codes' => $生成的激活码,
            'count' => $成功数量,
            'export_data' => $导出数据,
            'statistics' => $this->激活码工具->统计激活码信息($激活码数据)
        ];
    }

    /**
     * 导入激活码
     */
    private function 导入激活码($数据) {
        $激活码 = trim($数据['auth_code'] ?? '');
        $初始点数 = $数据['initial_points'] ?? 9999;
        $备注 = $数据['remark'] ?? '批量导入';
        $云数据 = $数据['cloud_data'] ?? '';  // 新增云数据支持

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        if (strlen($激活码) < 6 || strlen($激活码) > 50) {
            throw new Exception('激活码长度必须在6-50个字符之间');
        }

        if ($初始点数 <= 0) {
            throw new Exception('初始点数必须大于0');
        }

        // 检查激活码是否已存在
        $已存在 = $this->数据库->查询("SELECT COUNT(*) as count FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
        if ($已存在[0]['count'] > 0) {
            throw new Exception('激活码已存在');
        }

        // 插入激活码到数据库，包含云数据
        try {
            $sql = "INSERT INTO `激活码表` (`激活码`, `激活码状态`, `初始点数`, `剩余点数`, `每次扣除点数`, `最大解绑次数`, `云数据`, `备注`, `创建时间`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $this->数据库->执行($sql, [
                $激活码,
                0, // 激活码状态：0=未使用
                $初始点数,
                $初始点数, // 剩余点数等于初始点数
                1, // 每次扣除点数默认为1
                3, // 最大解绑次数默认为3
                $云数据, // 云数据
                $备注
            ]);

            $返回消息 = "激活码 {$激活码} 导入成功";
            if (!empty($云数据)) {
                $返回消息 .= "，云数据已设置";
            }

            return [
                'status' => 'success',
                'message' => $返回消息,
                'auth_code' => $激活码,
                'initial_points' => $初始点数,
                'cloud_data' => $云数据
            ];

        } catch (Exception $e) {
            throw new Exception('导入激活码失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量导入激活码
     */
    private function 批量导入激活码($数据) {
        $激活码数据列表 = $数据['auth_codes_data'] ?? [];
        $初始点数 = $数据['initial_points'] ?? 9999;
        $备注 = $数据['remark'] ?? '批量导入';

        if (empty($激活码数据列表)) {
            throw new Exception('激活码数据列表不能为空');
        }

        if (!is_array($激活码数据列表)) {
            throw new Exception('激活码数据列表格式不正确');
        }

        if (count($激活码数据列表) > 1000) {
            throw new Exception('最多只能导入1000个激活码');
        }

        if ($初始点数 <= 0) {
            throw new Exception('初始点数必须大于0');
        }

        $成功数量 = 0;
        $失败数量 = 0;
        $成功列表 = [];
        $失败详情 = [];

        // 开始事务
        $this->数据库->开始事务();

        try {
            foreach ($激活码数据列表 as $数据项) {
                try {
                    $激活码 = trim($数据项['auth_code'] ?? '');
                    $云数据 = trim($数据项['cloud_data'] ?? '');

                    if (empty($激活码)) {
                        $失败数量++;
                        $失败详情[] = "空激活码";
                        continue;
                    }

                    if (strlen($激活码) < 6 || strlen($激活码) > 50) {
                        $失败数量++;
                        $失败详情[] = "{$激活码}: 激活码长度必须在6-50个字符之间";
                        continue;
                    }

                    // 检查激活码是否已存在
                    $已存在 = $this->数据库->查询("SELECT COUNT(*) as count FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
                    if ($已存在[0]['count'] > 0) {
                        $失败数量++;
                        $失败详情[] = "{$激活码}: 激活码已存在";
                        continue;
                    }

                    // 插入激活码到数据库
                    $sql = "INSERT INTO `激活码表` (`激活码`, `激活码状态`, `初始点数`, `剩余点数`, `每次扣除点数`, `最大解绑次数`, `云数据`, `备注`, `创建时间`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                    $结果 = $this->数据库->执行($sql, [
                        $激活码,
                        0, // 激活码状态：0=未使用
                        $初始点数,
                        $初始点数, // 剩余点数等于初始点数
                        1, // 每次扣除点数默认为1
                        3, // 最大解绑次数默认为3
                        $云数据, // 云数据
                        $备注
                    ]);

                    if ($结果['success']) {
                        $成功数量++;
                        $成功列表[] = $激活码;
                    } else {
                        $失败数量++;
                        $失败详情[] = "{$激活码}: 数据库插入失败";
                    }

                } catch (Exception $e) {
                    $失败数量++;
                    $失败详情[] = "{$激活码}: " . $e->getMessage();
                }
            }

            $this->数据库->提交事务();

            $结果消息 = "批量导入完成: 成功{$成功数量}个, 失败{$失败数量}个";

            return [
                'status' => 'success',
                'message' => $结果消息,
                'data' => [
                    'success_count' => $成功数量,
                    'failed_count' => $失败数量,
                    'success_codes' => $成功列表,
                    'failed_details' => array_slice($失败详情, 0, 10) // 只返回前10个失败详情
                ]
            ];

        } catch (Exception $e) {
            $this->数据库->回滚事务();
            throw new Exception('批量导入激活码失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改点数
     */
    private function 修改点数($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $操作类型 = $数据['operation'] ?? 'add'; // add 或 subtract
        $点数 = $数据['points'] ?? 0;
        $原因 = $数据['reason'] ?? '管理员操作';
        
        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }
        
        if ($点数 <= 0) {
            throw new Exception('点数必须大于0');
        }
        
        if ($操作类型 === 'add') {
            $结果 = $this->数据库->增加激活码点数($激活码, $点数, $原因, 'admin');
        } elseif ($操作类型 === 'subtract') {
            $结果 = $this->数据库->扣除激活码点数($激活码, $点数, $原因, 'admin');
        } else {
            throw new Exception('无效的操作类型');
        }
        
        if (!$结果['success']) {
            throw new Exception($结果['error']);
        }
        
        return [
            'status' => 'success',
            'message' => '点数修改成功',
            'points_info' => [
                'before_points' => $结果['before_points'],
                'operation' => $操作类型,
                'changed_points' => $操作类型 === 'add' ? $结果['added_points'] : $结果['deducted_points'],
                'after_points' => $结果['after_points']
            ]
        ];
    }
    
    /**
     * 封禁目标
     */
    private function 封禁目标($数据) {
        $封禁类型 = $数据['ban_type'] ?? '';
        $封禁值 = $数据['ban_value'] ?? '';
        $封禁原因 = $数据['ban_reason'] ?? '';
        $解封时间 = $数据['unban_time'] ?? null;
        
        if (empty($封禁类型) || empty($封禁值) || empty($封禁原因)) {
            throw new Exception('封禁类型、封禁值和封禁原因不能为空');
        }
        
        $允许的封禁类型 = ['激活码', 'IP', '机器码'];
        if (!in_array($封禁类型, $允许的封禁类型)) {
            throw new Exception('无效的封禁类型');
        }
        
        if ($封禁类型 === '激活码') {
            $结果 = $this->数据库->封禁激活码($封禁值, $封禁原因, 'admin');
        } else {
            $结果 = $this->数据库->添加封禁($封禁类型, $封禁值, $封禁原因, $解封时间, 'admin');
            $结果 = ['success' => $结果['success'], 'message' => '封禁成功'];
        }
        
        if (!$结果['success']) {
            throw new Exception($结果['message']);
        }
        
        return [
            'status' => 'success',
            'message' => $结果['message']
        ];
    }
    
    /**
     * 解除封禁
     */
    private function 解除封禁($数据) {
        $封禁类型 = $数据['ban_type'] ?? '';
        $封禁值 = $数据['ban_value'] ?? '';

        if (empty($封禁类型) || empty($封禁值)) {
            throw new Exception('封禁类型和封禁值不能为空');
        }

        if ($封禁类型 === '激活码') {
            $结果 = $this->数据库->解封激活码($封禁值, 'admin');
        } else {
            $结果 = $this->数据库->解除封禁($封禁类型, $封禁值, 'admin');
            $结果 = ['success' => $结果['success'], 'message' => '解封成功'];
        }

        if (!$结果['success']) {
            throw new Exception($结果['message']);
        }

        return [
            'status' => 'success',
            'message' => $结果['message']
        ];
    }

    /**
     * 获取封禁列表
     */
    private function 获取封禁列表($数据) {
        $限制 = $数据['limit'] ?? 100;

        $封禁列表 = $this->数据库->获取封禁列表($限制);

        return [
            'status' => 'success',
            'message' => '获取封禁列表成功',
            'data' => $封禁列表,
            'count' => count($封禁列表)
        ];
    }

    /**
     * 获取系统统计
     */
    private function 获取系统统计($数据) {
        $统计数据 = $this->数据库->获取系统统计();

        return [
            'status' => 'success',
            'message' => '获取系统统计成功',
            'stats' => $统计数据
        ];
    }

    /**
     * 获取登录日志
     */
    private function 获取登录日志($数据) {
        $限制 = $数据['limit'] ?? 100;
        $激活码过滤 = $数据['auth_code_filter'] ?? $数据['auth_code'] ?? null;

        $sql = "SELECT * FROM `登录记录表`";
        $参数 = [];

        if ($激活码过滤) {
            $sql .= " WHERE `激活码` = ?";
            $参数[] = $激活码过滤;
        }

        $sql .= " ORDER BY `登录时间` DESC LIMIT ?";
        $参数[] = $限制;

        $登录日志 = $this->数据库->查询($sql, $参数);

        return [
            'status' => 'success',
            'message' => '获取登录日志成功',
            'data' => $登录日志,
            'count' => count($登录日志)
        ];
    }

    /**
     * 获取点数记录
     */
    private function 获取点数记录($数据) {
        $限制 = $数据['limit'] ?? 100;
        $激活码过滤 = $数据['auth_code_filter'] ?? null;

        $点数记录 = $this->数据库->获取点数变动记录($激活码过滤, $限制);

        return [
            'status' => 'success',
            'message' => '获取点数记录成功',
            'data' => $点数记录,
            'count' => count($点数记录)
        ];
    }

    /**
     * 获取IP地理位置统计
     */
    private function 获取IP地理位置统计($数据) {
        $限制 = $数据['limit'] ?? 20;

        $统计数据 = $this->数据库->获取IP地理位置统计($限制);

        // 获取总登录次数
        $总登录次数 = $this->数据库->查询单条("SELECT COUNT(*) as total FROM `登录记录表`");
        $成功登录次数 = $this->数据库->查询单条("SELECT COUNT(*) as total FROM `登录记录表` WHERE `登录状态` = 1");

        // 获取最近7天的登录地区分布
        $最近登录分布 = $this->数据库->查询("
            SELECT `ip_country`, `ip_province`, `ip_city`, COUNT(*) as count
            FROM `登录记录表`
            WHERE `登录时间` >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND `ip_country` IS NOT NULL
                AND `ip_country` != '未知'
            GROUP BY `ip_country`, `ip_province`, `ip_city`
            ORDER BY count DESC
            LIMIT ?
        ", [$限制]);

        return [
            'status' => 'success',
            'message' => '获取IP地理位置统计成功',
            'data' => [
                'total_logins' => $总登录次数['total'],
                'success_logins' => $成功登录次数['total'],
                'country_stats' => $统计数据['country_stats'],
                'province_stats' => $统计数据['province_stats'],
                'city_stats' => $统计数据['city_stats'],
                'isp_stats' => $统计数据['isp_stats'],
                'recent_locations' => $最近登录分布
            ]
        ];
    }

    /**
     * 管理云数据（纯文本格式）
     */
    private function 管理云数据($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $操作 = $数据['operation'] ?? 'get'; // get, set, clear

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        switch ($操作) {
            case 'get':
                $云数据 = $this->数据库->获取云数据($激活码);
                return [
                    'status' => 'success',
                    'cloud_data' => $云数据 // 直接返回纯文本
                ];

            case 'set':
                $新云数据 = $数据['cloud_data'] ?? '';
                // 允许空字符串，用于清除云数据

                $结果 = $this->数据库->设置云数据($激活码, $新云数据);
                if (!$结果['success']) {
                    throw new Exception('云数据设置失败');
                }

                return [
                    'status' => 'success',
                    'message' => '云数据设置成功',
                    'data_length' => strlen($新云数据)
                ];

            case 'clear':
                $结果 = $this->数据库->设置云数据($激活码, '');
                if (!$结果['success']) {
                    throw new Exception('云数据清除失败');
                }

                return [
                    'status' => 'success',
                    'message' => '云数据清除成功'
                ];

            default:
                throw new Exception('无效的操作类型');
        }
    }

    /**
     * 重置解绑次数
     */
    private function 重置解绑次数($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        $sql = "UPDATE `激活码表` SET `每日解绑次数` = 0, `解绑重置时间` = ? WHERE `激活码` = ?";
        $结果 = $this->数据库->执行($sql, [date('Y-m-d'), $激活码]);

        if (!$结果['success']) {
            throw new Exception('重置解绑次数失败');
        }

        return [
            'status' => 'success',
            'message' => '解绑次数重置成功'
        ];
    }

    /**
     * 管理员解绑设备（直接清理机器码）
     */
    private function 管理员解绑设备($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        // 直接清除绑定机器码，不受解绑次数限制
        $sql = "UPDATE `激活码表` SET `绑定机器码` = NULL WHERE `激活码` = ?";
        $结果 = $this->数据库->执行($sql, [$激活码]);

        if (!$结果['success']) {
            throw new Exception('设备解绑失败');
        }

        return [
            'status' => 'success',
            'message' => '设备解绑成功'
        ];
    }

    /**
     * 获取激活码详细信息
     */
    private function 获取激活码详细信息($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        $sql = "SELECT * FROM `激活码表` WHERE `激活码` = ?";
        $激活码信息 = $this->数据库->查询单条($sql, [$激活码]);

        if (!$激活码信息) {
            throw new Exception('激活码不存在');
        }

        // 格式化状态文本
        $状态映射 = [
            0 => '未使用',
            1 => '已使用',
            2 => '已封禁'
        ];
        $激活码信息['状态文本'] = $状态映射[$激活码信息['激活码状态']] ?? '未知';

        return [
            'status' => 'success',
            'data' => $激活码信息
        ];
    }

    /**
     * 删除激活码
     */
    private function 删除激活码($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        // 开始事务
        $this->数据库->开始事务();

        try {
            // 删除相关记录
            $this->数据库->执行("DELETE FROM `登录记录表` WHERE `激活码` = ?", [$激活码]);
            $this->数据库->执行("DELETE FROM `点数变动记录表` WHERE `激活码` = ?", [$激活码]);
            $this->数据库->执行("DELETE FROM `封禁列表表` WHERE `封禁类型` = '激活码' AND `封禁值` = ?", [$激活码]);

            // 删除激活码
            $结果 = $this->数据库->执行("DELETE FROM `激活码表` WHERE `激活码` = ?", [$激活码]);

            if (!$结果['success'] || $结果['affected_rows'] == 0) {
                throw new Exception('激活码删除失败');
            }

            $this->数据库->提交事务();

            return [
                'status' => 'success',
                'message' => '激活码删除成功'
            ];

        } catch (Exception $e) {
            $this->数据库->回滚事务();
            throw $e;
        }
    }

    /**
     * 批量删除激活码
     */
    private function 批量删除激活码($数据) {
        $激活码列表 = $数据['auth_codes'] ?? [];

        if (empty($激活码列表)) {
            throw new Exception('激活码列表不能为空');
        }

        if (!is_array($激活码列表)) {
            throw new Exception('激活码列表格式不正确');
        }

        if (count($激活码列表) > 1000) {
            throw new Exception('最多只能删除1000个激活码');
        }

        $成功数量 = 0;
        $失败数量 = 0;
        $成功列表 = [];
        $失败详情 = [];

        // 开始事务
        $this->数据库->开始事务();

        try {
            foreach ($激活码列表 as $激活码) {
                try {
                    $激活码 = trim($激活码);
                    if (empty($激活码)) {
                        $失败数量++;
                        $失败详情[] = "空激活码";
                        continue;
                    }

                    // 删除相关记录
                    $this->数据库->执行("DELETE FROM `登录记录表` WHERE `激活码` = ?", [$激活码]);
                    $this->数据库->执行("DELETE FROM `点数变动记录表` WHERE `激活码` = ?", [$激活码]);
                    $this->数据库->执行("DELETE FROM `封禁列表表` WHERE `封禁类型` = '激活码' AND `封禁值` = ?", [$激活码]);

                    // 删除激活码
                    $结果 = $this->数据库->执行("DELETE FROM `激活码表` WHERE `激活码` = ?", [$激活码]);

                    if ($结果['success'] && $结果['affected_rows'] > 0) {
                        $成功数量++;
                        $成功列表[] = $激活码;
                    } else {
                        $失败数量++;
                        $失败详情[] = "{$激活码}: 激活码不存在或删除失败";
                    }

                } catch (Exception $e) {
                    $失败数量++;
                    $失败详情[] = "{$激活码}: " . $e->getMessage();
                }
            }

            $this->数据库->提交事务();

            $结果消息 = "批量删除完成: 成功{$成功数量}个, 失败{$失败数量}个";

            return [
                'status' => 'success',
                'message' => $结果消息,
                'data' => [
                    'success_count' => $成功数量,
                    'failed_count' => $失败数量,
                    'success_codes' => $成功列表,
                    'failed_details' => array_slice($失败详情, 0, 10) // 只返回前10个失败详情
                ]
            ];

        } catch (Exception $e) {
            $this->数据库->回滚事务();
            throw new Exception('批量删除激活码失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化位置信息
     */
    private function 格式化位置信息($位置数据) {
        if (!$位置数据) {
            return '位置未知';
        }

        $位置部分 = [];

        if ($位置数据['ip_country'] && $位置数据['ip_country'] !== '未知') {
            $位置部分[] = $位置数据['ip_country'];
        }

        if ($位置数据['ip_province'] && $位置数据['ip_province'] !== '未知') {
            $位置部分[] = $位置数据['ip_province'];
        }

        if ($位置数据['ip_city'] && $位置数据['ip_city'] !== '未知') {
            $位置部分[] = $位置数据['ip_city'];
        }

        if ($位置数据['ip_area'] && $位置数据['ip_area'] !== '未知') {
            $位置部分[] = $位置数据['ip_area'];
        }

        $位置字符串 = implode(' ', $位置部分);

        if ($位置数据['ip_isp'] && $位置数据['ip_isp'] !== '未知') {
            $位置字符串 .= ' (' . $位置数据['ip_isp'] . ')';
        }

        return $位置字符串 ?: '位置未知';
    }

    /**
     * 修改解绑次数限制
     */
    private function 修改解绑次数限制($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $最大解绑次数 = $数据['max_unbind_count'] ?? 3;

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        if (!is_numeric($最大解绑次数) || $最大解绑次数 < 0 || $最大解绑次数 > 10) {
            throw new Exception('解绑次数限制必须在0-10之间');
        }

        $sql = "UPDATE `激活码表` SET `最大解绑次数` = ? WHERE `激活码` = ?";
        $结果 = $this->数据库->执行($sql, [$最大解绑次数, $激活码]);

        if (!$结果['success'] || $结果['affected_rows'] == 0) {
            throw new Exception('修改解绑次数限制失败，激活码可能不存在');
        }

        return [
            'status' => 'success',
            'message' => '解绑次数限制修改成功',
            'new_limit' => $最大解绑次数
        ];
    }

    /**
     * 设置云数据（纯文本格式）
     */
    private function 设置云数据($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $云数据 = $数据['cloud_data'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        // 云数据长度限制（可选）
        if (strlen($云数据) > 10000) {
            throw new Exception('云数据内容过长，请控制在10000字符以内');
        }

        // 使用数据库管理类的方法来设置云数据
        $结果 = $this->数据库->设置云数据($激活码, $云数据);

        if (!$结果['success']) {
            throw new Exception('设置云数据失败，激活码可能不存在');
        }

        return [
            'status' => 'success',
            'message' => '云数据设置成功',
            'data_length' => strlen($云数据)
        ];
    }

    /**
     * 返回成功响应
     */
    private function 返回成功响应($数据) {
        $响应 = $this->加密工具->创建安全响应($数据);
        echo json_encode($响应, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 返回错误响应
     */
    private function 返回错误响应($错误信息) {
        $错误数据 = [
            'status' => 'error',
            'message' => $错误信息,
            'timestamp' => time()
        ];

        $响应 = $this->加密工具->创建安全响应($错误数据);
        echo json_encode($响应, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取系统配置
     */
    private function 获取系统配置($数据) {
        try {
            $配置键 = $数据['config_key'] ?? null;
            $配置列表 = $this->数据库->获取系统配置($配置键);

            return [
                'status' => 'success',
                'message' => '获取系统配置成功',
                'data' => $配置列表
            ];

        } catch (Exception $e) {
            throw new Exception('获取系统配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新系统配置
     */
    private function 更新系统配置($数据) {
        try {
            $配置键 = $数据['config_key'] ?? '';
            $配置值 = $数据['config_value'] ?? '';

            if (empty($配置键)) {
                throw new Exception('配置键不能为空');
            }

            // 特殊验证软件标识必须是18位
            if ($配置键 === 'software_id' && strlen($配置值) !== 18) {
                throw new Exception('软件标识必须是18位');
            }

            // 特殊验证版本号格式
            if ($配置键 === 'software_version' && !preg_match('/^\d+\.\d+\.\d+$/', $配置值)) {
                throw new Exception('版本号格式不正确，应为 x.x.x 格式');
            }

            $结果 = $this->数据库->更新系统配置($配置键, $配置值);

            return [
                'status' => 'success',
                'message' => '系统配置更新成功',
                'data' => $结果
            ];

        } catch (Exception $e) {
            throw new Exception('更新系统配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取日志列表
     */
    private function 获取日志列表($数据) {
        try {
            $级别筛选 = $数据['level'] ?? '';
            $时间范围 = $数据['time_range'] ?? '';
            $IP筛选 = $数据['ip'] ?? '';
            $关键词筛选 = $数据['keyword'] ?? '';
            $页码 = max(1, intval($数据['page'] ?? 1));
            $限制 = max(1, min(1000, intval($数据['limit'] ?? 100)));

            $日志条目 = $this->读取日志文件([
                'level' => $级别筛选,
                'time_range' => $时间范围,
                'ip' => $IP筛选,
                'keyword' => $关键词筛选
            ]);

            $总数 = count($日志条目);
            $开始索引 = ($页码 - 1) * $限制;
            $分页日志 = array_slice($日志条目, $开始索引, $限制);

            return [
                'data' => $分页日志,
                'total' => $总数,
                'page' => $页码,
                'limit' => $限制,
                'total_pages' => ceil($总数 / $限制)
            ];
        } catch (Exception $e) {
            return [
                'data' => [],
                'total' => 0,
                'page' => 1,
                'limit' => 100,
                'total_pages' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取日志统计
     */
    private function 获取日志统计($数据) {
        try {
            $日志条目 = $this->读取日志文件();

            $统计信息 = [
                '总日志条数' => count($日志条目),
                '错误日志' => 0,
                '警告日志' => 0,
                '今日日志' => 0,
                '级别统计' => [],
                'IP统计' => []
            ];
        } catch (Exception $e) {
            // 如果读取日志失败，返回默认统计
            return [
                '总日志条数' => 0,
                '错误日志' => 0,
                '警告日志' => 0,
                '今日日志' => 0,
                '级别统计' => [],
                'IP统计' => [],
                'error' => $e->getMessage()
            ];
        }

        $今日开始 = strtotime('today');

        foreach ($日志条目 as $日志项) {
            // 级别统计
            $级别 = $日志项['级别'];
            if (!isset($统计信息['级别统计'][$级别])) {
                $统计信息['级别统计'][$级别] = 0;
            }
            $统计信息['级别统计'][$级别]++;

            // 错误和警告统计
            if ($级别 === 'ERROR' || $级别 === 'CRITICAL') {
                $统计信息['错误日志']++;
            } elseif ($级别 === 'WARNING') {
                $统计信息['警告日志']++;
            }

            // 今日日志统计
            $日志时间 = strtotime($日志项['时间']);
            if ($日志时间 >= $今日开始) {
                $统计信息['今日日志']++;
            }

            // IP统计（只统计前100个）
            $IP = $日志项['IP地址'];
            if ($IP !== '未知' && count($统计信息['IP统计']) < 100) {
                if (!isset($统计信息['IP统计'][$IP])) {
                    $统计信息['IP统计'][$IP] = 0;
                }
                $统计信息['IP统计'][$IP]++;
            }
        }

        // 按访问次数排序IP统计
        arsort($统计信息['IP统计']);

        return $统计信息;
    }

    /**
     * 导出日志
     */
    private function 导出日志($数据) {
        $筛选条件 = [
            'level' => $数据['level'] ?? '',
            'time_range' => $数据['time_range'] ?? '',
            'ip' => $数据['ip'] ?? '',
            'keyword' => $数据['keyword'] ?? ''
        ];

        $日志条目 = $this->读取日志文件($筛选条件);

        $导出内容 = "# 日志导出文件\n";
        $导出内容 .= "# 导出时间: " . date('Y-m-d H:i:s') . "\n";
        $导出内容 .= "# 总条数: " . count($日志条目) . "\n";
        $导出内容 .= "# 筛选条件: " . json_encode($筛选条件, JSON_UNESCAPED_UNICODE) . "\n";
        $导出内容 .= str_repeat("=", 80) . "\n\n";

        foreach ($日志条目 as $日志项) {
            $导出内容 .= $日志项['原始内容'] . "\n";
        }

        return [
            'content' => $导出内容,
            'filename' => 'logs_' . date('Y-m-d_H-i-s') . '.txt',
            'count' => count($日志条目)
        ];
    }

    /**
     * 清理日志
     */
    private function 清理日志($数据) {
        $天数 = max(1, intval($数据['days'] ?? 30));
        $清理时间 = time() - ($天数 * 24 * 60 * 60);
        $清理数量 = 0;

        $日志文件列表 = $this->获取日志文件列表();
        foreach ($日志文件列表 as $文件信息) {
            if ($文件信息['modified'] < $清理时间) {
                if (unlink($文件信息['path'])) {
                    $清理数量++;
                }
            }
        }

        return [
            'message' => "成功清理了 {$清理数量} 个旧日志文件",
            'cleaned_count' => $清理数量
        ];
    }

    /**
     * 获取所有日志文件列表
     */
     private function 获取日志文件列表() {
        $日志文件 = [];
        $调试信息 = [];

        // 记录当前工作目录
        $调试信息['当前目录'] = getcwd();

        // 扫描system_logs目录
        $system_logs_path = 'system_logs';
        $调试信息['system_logs目录存在'] = is_dir($system_logs_path);
        if (is_dir($system_logs_path)) {
            $文件列表 = glob($system_logs_path . '/*.txt');
            $调试信息['system_logs文件数量'] = count($文件列表);
            foreach ($文件列表 as $文件) {
                if (file_exists($文件) && is_readable($文件)) {
                    $日志文件[] = [
                        'path' => $文件,
                        'name' => basename($文件),
                        'size' => filesize($文件),
                        'modified' => filemtime($文件),
                        'type' => 'system'
                    ];
                }
            }
        }

        // 扫描logs目录
        $logs_path = 'logs';
        $调试信息['logs目录存在'] = is_dir($logs_path);
        if (is_dir($logs_path)) {
            $文件列表 = glob($logs_path . '/*.txt');
            $调试信息['logs文件数量'] = count($文件列表);
            foreach ($文件列表 as $文件) {
                if (file_exists($文件) && is_readable($文件)) {
                    $日志文件[] = [
                        'path' => $文件,
                        'name' => basename($文件),
                        'size' => filesize($文件),
                        'modified' => filemtime($文件),
                        'type' => 'application'
                    ];
                }
            }
        }

        // 按修改时间倒序排列
        usort($日志文件, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });

        $调试信息['找到的日志文件数量'] = count($日志文件);

        // 如果没有找到日志文件，抛出异常包含调试信息
        if (empty($日志文件)) {
            throw new Exception('未找到日志文件: ' . json_encode($调试信息, JSON_UNESCAPED_UNICODE));
        }

        return $日志文件;
    }

    /**
     * 解析日志行
     */
    private function 解析日志行($行内容) {
        $行内容 = trim($行内容);
        if (empty($行内容)) {
            return null;
        }

        // 尝试解析标准格式：[时间] [级别-中文级别] [IP:xxx] 消息 | 附加数据:xxx | UA:xxx
        if (preg_match('/^\[([^\]]+)\]\s*\[([^\]]+)\]\s*\[IP:([^\]]+)\]\s*(.+)$/', $行内容, $matches)) {
            $时间 = $matches[1];
            $级别信息 = $matches[2];
            $IP地址 = $matches[3];
            $剩余内容 = $matches[4];

            // 解析级别信息
            $级别 = 'INFO';
            if (preg_match('/^([A-Z]+)-/', $级别信息, $级别匹配)) {
                $级别 = $级别匹配[1];
            }

            // 解析消息和附加数据
            $消息内容 = $剩余内容;
            $附加数据 = '';
            $用户代理 = '';

            if (strpos($剩余内容, ' | ') !== false) {
                $部分 = explode(' | ', $剩余内容);
                $消息内容 = $部分[0];

                foreach ($部分 as $部分内容) {
                    if (strpos($部分内容, '附加数据:') === 0) {
                        $附加数据 = substr($部分内容, 5);
                    } elseif (strpos($部分内容, 'UA:') === 0) {
                        $用户代理 = substr($部分内容, 3);
                    }
                }
            }

            return [
                '时间' => $时间,
                '级别' => $级别,
                'IP地址' => $IP地址,
                '消息内容' => $消息内容,
                '附加数据' => $附加数据,
                '用户代理' => $用户代理,
                '原始内容' => $行内容
            ];
        }

        // 尝试解析简单格式
        if (preg_match('/^\[([^\]]+)\]\s*\[([^\]]+)\]\s*(.+)$/', $行内容, $matches)) {
            return [
                '时间' => $matches[1],
                '级别' => 'INFO',
                'IP地址' => '未知',
                '消息内容' => $matches[3],
                '附加数据' => '',
                '用户代理' => '',
                '原始内容' => $行内容
            ];
        }

        // 无法解析的行，作为普通消息处理
        return [
            '时间' => date('Y-m-d H:i:s'),
            '级别' => 'INFO',
            'IP地址' => '未知',
            '消息内容' => $行内容,
            '附加数据' => '',
            '用户代理' => '',
            '原始内容' => $行内容
        ];
    }

    /**
     * 读取和解析日志文件
     */
    private function 读取日志文件($筛选条件 = []) {
        $日志条目 = [];
        $日志文件列表 = $this->获取日志文件列表();

        $级别筛选 = $筛选条件['level'] ?? '';
        $时间范围 = $筛选条件['time_range'] ?? '';
        $IP筛选 = $筛选条件['ip'] ?? '';
        $关键词筛选 = $筛选条件['keyword'] ?? '';

        // 计算时间范围
        $开始时间 = null;
        $结束时间 = null;

        switch ($时间范围) {
            case 'today':
                $开始时间 = strtotime('today');
                $结束时间 = strtotime('tomorrow') - 1;
                break;
            case 'yesterday':
                $开始时间 = strtotime('yesterday');
                $结束时间 = strtotime('today') - 1;
                break;
            case 'week':
                $开始时间 = strtotime('-7 days');
                $结束时间 = time();
                break;
            case 'month':
                $开始时间 = strtotime('-30 days');
                $结束时间 = time();
                break;
        }

        foreach ($日志文件列表 as $文件信息) {
            if (!file_exists($文件信息['path'])) {
                continue;
            }

            $文件句柄 = fopen($文件信息['path'], 'r');
            if (!$文件句柄) {
                continue;
            }

            while (($行 = fgets($文件句柄)) !== false) {
                $日志项 = $this->解析日志行($行);
                if (!$日志项) {
                    continue;
                }

                // 应用筛选条件
                $通过筛选 = true;

                // 级别筛选
                if (!empty($级别筛选) && $日志项['级别'] !== $级别筛选) {
                    $通过筛选 = false;
                }

                // IP筛选
                if (!empty($IP筛选) && strpos($日志项['IP地址'], $IP筛选) === false) {
                    $通过筛选 = false;
                }

                // 关键词筛选
                if (!empty($关键词筛选)) {
                    $搜索内容 = $日志项['消息内容'] . ' ' . $日志项['附加数据'] . ' ' . $日志项['用户代理'];
                    if (stripos($搜索内容, $关键词筛选) === false) {
                        $通过筛选 = false;
                    }
                }

                // 时间范围筛选
                if ($开始时间 && $结束时间) {
                    $日志时间 = strtotime($日志项['时间']);
                    if ($日志时间 < $开始时间 || $日志时间 > $结束时间) {
                        $通过筛选 = false;
                    }
                }

                if ($通过筛选) {
                    $日志项['文件'] = $文件信息['name'];
                    $日志项['文件类型'] = $文件信息['type'];
                    $日志条目[] = $日志项;

                    // 限制返回数量
                    if (count($日志条目) >= 10000) {
                        break 2;
                    }
                }
            }

            fclose($文件句柄);
        }

        // 按时间倒序排列
        usort($日志条目, function($a, $b) {
            return strtotime($b['时间']) - strtotime($a['时间']);
        });

        return $日志条目;
    }
}

// 处理请求
try {
    $api = new 新版管理员API();
    $api->处理请求();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
