<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>激活码筛选功能测试</h2>
        
        <!-- 筛选控件 -->
        <div class="row g-3 mb-4">
            <div class="col-md-2">
                <label class="form-label">使用状态</label>
                <select class="form-select form-select-sm" id="filter-usage-status">
                    <option value="">全部</option>
                    <option value="used">已使用</option>
                    <option value="unused">未使用</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">账号状态</label>
                <select class="form-select form-select-sm" id="filter-account-status">
                    <option value="">全部</option>
                    <option value="normal">正常</option>
                    <option value="banned">已封禁</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">绑定状态</label>
                <select class="form-select form-select-sm" id="filter-bind-status">
                    <option value="">全部</option>
                    <option value="bound">已绑定设备</option>
                    <option value="unbound">未绑定设备</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">云数据状态</label>
                <select class="form-select form-select-sm" id="filter-cloud-status">
                    <option value="">全部</option>
                    <option value="has-data">有云数据</option>
                    <option value="no-data">无云数据</option>
                    <option value="member">会员</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">搜索激活码</label>
                <input type="text" class="form-control form-control-sm" id="search-auth-code" placeholder="输入激活码">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-1">
                    <button class="btn btn-outline-secondary btn-sm" onclick="重置筛选()">重置筛选</button>
                    <button class="btn btn-outline-info btn-sm" onclick="测试筛选功能()">测试筛选</button>
                </div>
            </div>
        </div>

        <!-- 测试表格 -->
        <div class="table-responsive">
            <table id="auth-codes-table" class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all-codes"></th>
                        <th>激活码</th>
                        <th>使用状态</th>
                        <th>账号状态</th>
                        <th>剩余点数</th>
                        <th>绑定设备</th>
                        <th>云数据</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 测试数据 -->
                    <tr data-auth-code="TEST001" data-usage-status="used" data-account-status="正常" data-bind-status="bound" data-cloud-status="has-data">
                        <td><input type="checkbox" class="auth-code-checkbox" value="TEST001"></td>
                        <td><code>TEST001</code></td>
                        <td><span class="badge bg-success">已使用</span></td>
                        <td><span class="badge bg-success">正常</span></td>
                        <td>100</td>
                        <td>ABC123</td>
                        <td>用户数据</td>
                    </tr>
                    <tr data-auth-code="TEST002" data-usage-status="unused" data-account-status="正常" data-bind-status="unbound" data-cloud-status="no-data">
                        <td><input type="checkbox" class="auth-code-checkbox" value="TEST002"></td>
                        <td><code>TEST002</code></td>
                        <td><span class="badge bg-secondary">未使用</span></td>
                        <td><span class="badge bg-success">正常</span></td>
                        <td>200</td>
                        <td>未绑定</td>
                        <td>无</td>
                    </tr>
                    <tr data-auth-code="TEST003" data-usage-status="used" data-account-status="已封禁" data-bind-status="bound" data-cloud-status="member">
                        <td><input type="checkbox" class="auth-code-checkbox" value="TEST003"></td>
                        <td><code>TEST003</code></td>
                        <td><span class="badge bg-success">已使用</span></td>
                        <td><span class="badge bg-danger">已封禁</span></td>
                        <td>50</td>
                        <td>DEF456</td>
                        <td>会员</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="filter-result-info" class="mt-2 text-muted"></div>
    </div>

    <script>
        // 全新的筛选功能实现
        function 应用筛选() {
            console.log('=== 开始应用筛选 ===');

            // 获取所有表格行
            const $allRows = $('#auth-codes-table tbody tr');
            const totalRows = $allRows.length;

            if (totalRows === 0) {
                console.log('表格无数据，跳过筛选');
                return;
            }

            // 获取筛选条件
            const filters = {
                usage: $('#filter-usage-status').val() || '',
                account: $('#filter-account-status').val() || '',
                bind: $('#filter-bind-status').val() || '',
                cloud: $('#filter-cloud-status').val() || '',
                search: $('#search-auth-code').val().trim().toLowerCase() || ''
            };

            console.log('当前筛选条件:', filters);

            let visibleCount = 0;

            // 遍历每一行进行筛选
            $allRows.each(function(index) {
                const $row = $(this);
                let shouldShow = true;

                try {
                    // 获取行数据
                    const rowData = {
                        authCode: $row.find('td:eq(1) code').text().trim(),
                        usageStatus: $row.attr('data-usage-status') || '',
                        accountStatus: $row.attr('data-account-status') || '',
                        bindStatus: $row.attr('data-bind-status') || '',
                        cloudStatus: $row.attr('data-cloud-status') || ''
                    };

                    console.log(`行 ${index + 1} 数据:`, rowData);

                    // 1. 使用状态筛选
                    if (filters.usage && rowData.usageStatus !== filters.usage) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 使用状态不匹配: ${rowData.usageStatus} !== ${filters.usage}`);
                    }

                    // 2. 账号状态筛选
                    if (shouldShow && filters.account) {
                        if (filters.account === 'normal' && rowData.accountStatus !== '正常') {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 账号状态不匹配(正常): ${rowData.accountStatus}`);
                        } else if (filters.account === 'banned' && rowData.accountStatus !== '已封禁') {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 账号状态不匹配(封禁): ${rowData.accountStatus}`);
                        }
                    }

                    // 3. 绑定状态筛选
                    if (shouldShow && filters.bind && rowData.bindStatus !== filters.bind) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 绑定状态不匹配: ${rowData.bindStatus} !== ${filters.bind}`);
                    }

                    // 4. 云数据状态筛选
                    if (shouldShow && filters.cloud && rowData.cloudStatus !== filters.cloud) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 云数据状态不匹配: ${rowData.cloudStatus} !== ${filters.cloud}`);
                    }

                    // 5. 搜索文本筛选（最重要的功能）
                    if (shouldShow && filters.search) {
                        const authCodeLower = rowData.authCode.toLowerCase();
                        if (!authCodeLower.includes(filters.search)) {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 搜索文本不匹配: "${authCodeLower}" 不包含 "${filters.search}"`);
                        } else {
                            console.log(`行 ${index + 1} 搜索文本匹配: "${authCodeLower}" 包含 "${filters.search}"`);
                        }
                    }

                    // 显示或隐藏行
                    if (shouldShow) {
                        $row.show();
                        visibleCount++;
                        console.log(`行 ${index + 1} 显示`);
                    } else {
                        $row.hide();
                        console.log(`行 ${index + 1} 隐藏`);
                    }

                } catch (error) {
                    console.error(`处理行 ${index + 1} 时出错:`, error);
                    $row.show(); // 出错时默认显示
                    visibleCount++;
                }
            });

            // 更新筛选结果显示
            const resultText = `筛选结果：显示 ${visibleCount} / ${totalRows} 条记录`;
            $('#filter-result-info').text(resultText);

            console.log(`=== 筛选完成: ${resultText} ===`);
        }

        // 重置筛选
        function 重置筛选() {
            $('#filter-usage-status').val('');
            $('#filter-account-status').val('');
            $('#filter-bind-status').val('');
            $('#filter-cloud-status').val('');
            $('#search-auth-code').val('');
            $('#auth-codes-table tbody tr').show();
            $('#filter-result-info').text('');
        }

        // 测试筛选功能
        function 测试筛选功能() {
            console.log('=== 筛选功能测试开始 ===');
            
            // 检查筛选控件是否存在
            const controls = [
                '#filter-usage-status',
                '#filter-account-status', 
                '#filter-bind-status',
                '#filter-cloud-status',
                '#search-auth-code'
            ];
            
            controls.forEach(selector => {
                const element = $(selector);
                console.log(`控件 ${selector}:`, element.length ? '存在' : '不存在', element.length ? `值: ${element.val()}` : '');
            });
            
            // 检查表格数据
            const totalRows = $('#auth-codes-table tbody tr').length;
            console.log(`表格总行数: ${totalRows}`);
            
            if (totalRows > 0) {
                const firstRow = $('#auth-codes-table tbody tr:first');
                console.log('第一行数据属性:', {
                    'data-auth-code': firstRow.data('auth-code'),
                    'data-usage-status': firstRow.data('usage-status'),
                    'data-account-status': firstRow.data('account-status'),
                    'data-bind-status': firstRow.data('bind-status'),
                    'data-cloud-status': firstRow.data('cloud-status')
                });
            }
            
            console.log('=== 筛选功能测试结束 ===');
        }

        // 绑定事件
        $(document).ready(function() {
            $('#filter-usage-status, #filter-account-status, #filter-bind-status, #filter-cloud-status').on('change', 应用筛选);
            $('#search-auth-code').on('input', 应用筛选);
            
            // 全选功能
            $('#select-all-codes').on('change', function() {
                $('.auth-code-checkbox:visible').prop('checked', this.checked);
            });
            
            console.log('事件绑定完成');
        });
    </script>
</body>
</html>
