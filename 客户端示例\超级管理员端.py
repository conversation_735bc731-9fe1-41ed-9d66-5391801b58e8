#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络验证系统超级管理员端 - Flask Web版本
提供基于Web的管理界面，支持激活码管理、用户管理等功能
"""

from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import requests
import json
import secrets
import logging
import hashlib
import time
from datetime import datetime, timedelta
from functools import wraps
from DES加密工具类 import DES加密工具类

# Flask应用配置
app = Flask(__name__)
app.secret_key = secrets.token_hex(32)  # 生成随机密钥
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# 简单的CSRF保护实现
def 生成CSRF令牌():
    """生成CSRF令牌"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(32)
    return session['csrf_token']

def 验证CSRF令牌(token):
    """验证CSRF令牌"""
    return token and session.get('csrf_token') == token

@app.context_processor
def 注入CSRF令牌():
    """向模板注入CSRF令牌"""
    return dict(csrf_token=生成CSRF令牌)

# 全局配置
服务器地址 = "http://222.186.21.133:8742"
管理员密码 = "admin123456"
加密工具 = DES加密工具类()

# 配置日志 - 只输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def 记录日志(级别, 消息, 详情=None):
    """记录日志信息"""
    try:
        日志消息 = f"{消息}"
        if 详情:
            if isinstance(详情, dict):
                详情字符串 = ", ".join([f"{k}={v}" for k, v in 详情.items()])
                日志消息 += f" - {详情字符串}"
            else:
                日志消息 += f" - {详情}"

        if 级别 == "INFO":
            logger.info(日志消息)
        elif 级别 == "WARNING":
            logger.warning(日志消息)
        elif 级别 == "ERROR":
            logger.error(日志消息)
        elif 级别 == "DEBUG":
            logger.debug(日志消息)

    except Exception as e:
        print(f"日志记录失败: {e}")

def 需要登录(f):
    """装饰器：已移除登录检查，直接允许访问"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 直接允许访问，不需要登录验证
        return f(*args, **kwargs)
    return decorated_function

def 需要CSRF验证(f):
    """装饰器：验证CSRF令牌"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method in ['POST', 'PUT', 'DELETE']:
            csrf_token = request.headers.get('X-CSRFToken') or request.form.get('csrf_token')
            if request.is_json:
                data = request.get_json() or {}
                csrf_token = csrf_token or data.get('csrf_token')

            if not 验证CSRF令牌(csrf_token):
                return jsonify({
                    'status': 'error',
                    'message': 'CSRF令牌验证失败'
                }), 403

        return f(*args, **kwargs)
    return decorated_function

def 生成会话令牌():
    """生成安全的会话令牌"""
    return hashlib.sha256(f"{secrets.token_hex(32)}{time.time()}".encode()).hexdigest()

def 验证请求频率(max_requests=100, time_window=60):
    """简单的请求频率限制"""
    client_ip = request.remote_addr
    current_time = time.time()

    # 这里可以使用Redis或内存缓存来存储请求记录
    # 为了简化，这里只是一个基本的实现
    if 'request_history' not in session:
        session['request_history'] = []

    # 清理过期的请求记录
    session['request_history'] = [
        req_time for req_time in session['request_history']
        if current_time - req_time < time_window
    ]

    # 检查请求频率
    if len(session['request_history']) >= max_requests:
        return False

    # 记录当前请求
    session['request_history'].append(current_time)
    return True

def 发送管理员请求(数据):
    """发送管理员API请求"""
    try:
        操作 = 数据.get('action', 'unknown')
        记录日志("INFO", f"发送管理员请求", {"操作": 操作})

        数据['admin_password'] = 管理员密码
        安全请求 = 加密工具.创建安全请求(数据)

        # 尝试新版API
        新版API地址 = f"{服务器地址}/新版管理员API.php"
        记录日志("DEBUG", f"请求地址", {"URL": 新版API地址})

        response = requests.post(
            新版API地址,
            json=安全请求,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        记录日志("INFO", f"服务器响应", {"状态码": response.status_code})

        if response.status_code != 200:
            记录日志("ERROR", f"HTTP错误", {"状态码": response.status_code, "响应": response.text[:200]})
            raise Exception(f"HTTP错误: {response.status_code}")

        响应数据 = response.json()
        记录日志("DEBUG", f"响应数据类型", {"类型": type(响应数据).__name__})

        if 'encrypted_data' in 响应数据:
            解密响应 = 加密工具.解析安全响应(响应数据['encrypted_data'])
            记录日志("INFO", f"请求成功", {"状态": 解密响应.get('status', 'unknown')})
            return 解密响应
        else:
            记录日志("WARNING", f"响应未加密", {"响应": str(响应数据)[:200]})
            return 响应数据

    except requests.exceptions.RequestException as e:
        记录日志("ERROR", f"网络请求异常", {"错误": str(e)})
        raise Exception(f"网络请求失败: {str(e)}")
    except json.JSONDecodeError as e:
        记录日志("ERROR", f"JSON解析失败", {"错误": str(e)})
        raise Exception(f"响应格式错误: {str(e)}")
    except Exception as e:
        记录日志("ERROR", f"请求处理失败", {"错误": str(e)})
        raise Exception(f"请求失败: {str(e)}")

# HTML模板定义
控制台页面模板 = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>网络验证系统 - 超级管理员端</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 通知系统样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
        }

        .notification {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            padding: 16px;
            border-left: 4px solid #28a745;
            animation: slideInRight 0.3s ease-out;
            position: relative;
            overflow: hidden;
        }

        .notification.success { border-left-color: #28a745; }
        .notification.error { border-left-color: #dc3545; }
        .notification.warning { border-left-color: #ffc107; }
        .notification.info { border-left-color: #17a2b8; }

        .notification .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .notification .notification-message {
            color: #666;
            font-size: 14px;
        }

        .notification .notification-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification .notification-close:hover {
            color: #333;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification.slide-out {
            animation: slideOutRight 0.3s ease-in forwards;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px 0;
        }
        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: #e9ecef;
            color: #667eea;
            border-left: 4px solid #667eea;
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-banned { background-color: #f8d7da; color: #721c24; }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e1e5e9;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* 优化按钮样式 */
        .btn-group-sm .btn {
            padding: 6px 12px;
            font-size: 13px;
            min-width: 80px;
            border-radius: 6px;
            margin: 0 2px;
        }

        .btn-outline-primary {
            color: #667eea;
            border-color: #667eea;
        }

        .btn-outline-primary:hover {
            background-color: #667eea;
            border-color: #667eea;
            transform: translateY(-1px);
        }

        .btn-outline-warning {
            color: #f39c12;
            border-color: #f39c12;
        }

        .btn-outline-warning:hover {
            background-color: #f39c12;
            border-color: #f39c12;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            color: #e74c3c;
            border-color: #e74c3c;
        }

        .btn-outline-danger:hover {
            background-color: #e74c3c;
            border-color: #e74c3c;
            transform: translateY(-1px);
        }

        .btn-outline-info {
            color: #3498db;
            border-color: #3498db;
        }

        .btn-outline-info:hover {
            background-color: #3498db;
            border-color: #3498db;
            transform: translateY(-1px);
        }

        .btn-outline-success {
            color: #27ae60;
            border-color: #27ae60;
        }

        .btn-outline-success:hover {
            background-color: #27ae60;
            border-color: #27ae60;
            transform: translateY(-1px);
        }

        /* 激活码可点击复制样式 */
        .auth-code-clickable {
            cursor: pointer;
            color: #667eea;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s;
        }

        .auth-code-clickable:hover {
            color: #764ba2;
            text-decoration: underline;
            transform: scale(1.02);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .dropdown-menu {
            border-radius: 10px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i> 网络验证系统 - 超级管理员端
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="刷新系统状态()"><i class="bi bi-arrow-clockwise"></i> 刷新状态</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" onclick="显示页面('dashboard')">
                        <i class="bi bi-speedometer2"></i> 控制台
                    </a>
                    <a class="nav-link" href="#" onclick="显示页面('auth_codes')">
                        <i class="bi bi-key"></i> 激活码管理
                    </a>
                    <a class="nav-link" href="#" onclick="显示页面('system_config')">
                        <i class="bi bi-gear"></i> 系统配置
                    </a>
                    <a class="nav-link" href="#" onclick="显示页面('log_analysis')">
                        <i class="bi bi-file-text"></i> 日志分析
                    </a>
                </nav>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 控制台页面 -->
                <div id="dashboard-page" class="page-content">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="total-codes">-</div>
                                        <div>激活码总数</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-key" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="active-codes">-</div>
                                        <div>活跃激活码</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="total-logins">-</div>
                                        <div>总登录次数</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="banned-count">-</div>
                                        <div>封禁数量</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-shield-x" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle"></i> 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>服务器地址:</strong> {{ 服务器地址 }}</p>
                                    <p><strong>当前时间:</strong> <span id="current-time">{{ 当前时间 }}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>数据库状态:</strong> <span id="db-status" class="badge bg-success">正常</span></p>
                                    <p><strong>API状态:</strong> <span id="api-status" class="badge bg-success">正常</span></p>
                                    <p><strong>最后更新:</strong> <span id="last-update">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面内容将在后续添加 -->
                <div id="auth_codes-page" class="page-content" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="bi bi-key"></i> 激活码管理</h5>
                                <div>
                                    <button class="btn btn-primary btn-sm" onclick="显示生成激活码对话框()">
                                        <i class="bi bi-plus"></i> 生成激活码
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="显示批量导入激活码对话框()">
                                        <i class="bi bi-upload"></i> 批量导入激活码
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="刷新激活码列表()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>

                            <!-- 筛选控件 -->
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">使用状态</label>
                                    <select class="form-select form-select-sm" id="filter-usage-status">
                                        <option value="">全部</option>
                                        <option value="used">已使用</option>
                                        <option value="unused">未使用</option>

                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">账号状态</label>
                                    <select class="form-select form-select-sm" id="filter-account-status">
                                        <option value="">全部</option>
                                        <option value="normal">正常</option>
                                        <option value="banned">已封禁</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">绑定状态</label>
                                    <select class="form-select form-select-sm" id="filter-bind-status">
                                        <option value="">全部</option>
                                        <option value="bound">已绑定设备</option>
                                        <option value="unbound">未绑定设备</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">云数据状态</label>
                                    <select class="form-select form-select-sm" id="filter-cloud-status">
                                        <option value="">全部</option>
                                        <option value="has-data">有云数据</option>
                                        <option value="no-data">无云数据</option>
                                        <option value="member">会员</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">搜索激活码</label>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" id="search-auth-code" placeholder="输入激活码">
                                        <button class="btn btn-outline-primary" type="button" onclick="强制搜索()" title="强制搜索">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-1">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="重置筛选()">
                                            <i class="bi bi-arrow-clockwise"></i> 重置筛选
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="测试筛选功能()" title="测试筛选功能">
                                            <i class="bi bi-bug"></i> 测试筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="loading" id="auth-codes-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载激活码列表...</p>
                            </div>
                            <div class="table-responsive">
                                <table id="auth-codes-table" class="table table-striped table-hover" style="display: none;">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="select-all-codes"></th>
                                            <th>激活码</th>
                                            <th>使用状态</th>
                                            <th>账号状态</th>
                                            <th>剩余点数</th>
                                            <th>解绑次数</th>

                                            <th>绑定设备</th>
                                            <th>云数据</th>
                                            <th>最新登录位置</th>
                                            <th>最后登录</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>

                            <!-- 批量操作区域 -->
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="bi bi-gear"></i> 批量操作</h6>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-outline-warning btn-sm" onclick="批量解绑设备()">
                                                <i class="bi bi-unlink"></i> 批量解绑设备
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" onclick="批量重置解绑次数()">
                                                <i class="bi bi-arrow-clockwise"></i> 批量重置解绑次数
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="批量登记会员()">
                                                <i class="bi bi-person-plus"></i> 批量登记会员
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="批量清除云数据()">
                                                <i class="bi bi-trash"></i> 批量清除云数据
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="批量删除激活码()">
                                                <i class="bi bi-trash3"></i> 批量删除激活码
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="bi bi-shield-x"></i> 封禁操作</h6>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-outline-primary btn-sm" onclick="显示添加封禁对话框()">
                                                <i class="bi bi-plus"></i> 添加封禁
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="显示封禁列表()">
                                                <i class="bi bi-list"></i> 查看封禁列表
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="批量解封()">
                                                <i class="bi bi-unlock"></i> 批量解封
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统配置页面 -->
                <div id="system_config-page" class="page-content" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-gear"></i> 系统配置</h5>
                                <button class="btn btn-outline-primary btn-sm" onclick="刷新系统配置()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="loading" id="system-config-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载系统配置...</p>
                            </div>

                            <div id="system-config-content" style="display: none;">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6><i class="bi bi-sliders"></i> 核心配置</h6>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>配置项</th>
                                                        <th>当前值</th>
                                                        <th>描述</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="system-config-table">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="bi bi-info-circle"></i> 配置说明</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="alert alert-info">
                                                    <h6>重要提示：</h6>
                                                    <ul class="mb-0">
                                                        <li><strong>软件标识</strong>：必须是18位字符，客户端验证时必须匹配</li>
                                                        <li><strong>软件版本号</strong>：格式为 x.x.x，客户端版本必须匹配</li>
                                                        <li><strong>解绑次数限制</strong>：每日最大解绑次数，0表示无限制</li>
                                                        <li><strong>默认点数</strong>：新生成激活码的默认点数</li>
                                                    </ul>
                                                </div>
                                                <div class="alert alert-warning">
                                                    <h6>注意事项：</h6>
                                                    <p class="mb-0">修改软件标识或版本号后，所有客户端都需要更新才能正常使用！</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志分析页面 -->
                <div id="log_analysis-page" class="page-content" style="display: none;">
                    <div class="row">
                        <!-- 日志统计卡片 -->
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="total-logs">-</div>
                                        <div>总日志条数</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-file-text" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="error-logs">-</div>
                                        <div>错误日志</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="warning-logs">-</div>
                                        <div>警告日志</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-exclamation-circle" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stats-number" id="today-logs">-</div>
                                        <div>今日日志</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-calendar-day" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="bi bi-file-text"></i> 日志分析</h5>
                                <div>
                                    <button class="btn btn-primary btn-sm" onclick="刷新日志列表()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新日志
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="导出日志()">
                                        <i class="bi bi-download"></i> 导出日志
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="清理旧日志()">
                                        <i class="bi bi-trash"></i> 清理旧日志
                                    </button>
                                </div>
                            </div>

                            <!-- 日志筛选控件 -->
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">日志级别</label>
                                    <select class="form-select form-select-sm" id="filter-log-level">
                                        <option value="">全部级别</option>
                                        <option value="DEBUG">调试信息</option>
                                        <option value="INFO">一般信息</option>
                                        <option value="WARNING">警告信息</option>
                                        <option value="ERROR">错误信息</option>
                                        <option value="CRITICAL">严重错误</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">时间范围</label>
                                    <select class="form-select form-select-sm" id="filter-time-range">
                                        <option value="">全部时间</option>
                                        <option value="today">今天</option>
                                        <option value="yesterday">昨天</option>
                                        <option value="week">最近7天</option>
                                        <option value="month">最近30天</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">IP地址</label>
                                    <input type="text" class="form-control form-control-sm" id="filter-ip" placeholder="输入IP地址">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">关键词搜索</label>
                                    <input type="text" class="form-control form-control-sm" id="filter-keyword" placeholder="搜索日志内容">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-1">
                                        <div class="btn-group">
                                            <button class="btn btn-outline-primary btn-sm" onclick="应用日志筛选()">
                                                <i class="bi bi-search"></i> 搜索
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="重置日志筛选()">
                                                <i class="bi bi-arrow-clockwise"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="loading" id="logs-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载日志数据...</p>
                            </div>
                            <div class="table-responsive">
                                <table id="logs-table" class="table table-striped table-hover" style="display: none;">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>级别</th>
                                            <th>IP地址</th>
                                            <th>消息内容</th>
                                            <th>用户代理</th>
                                            <th>附加数据</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 模态框占位符 -->
    <div id="modal-container"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // 全局变量
        let authCodesTable = null;

        // 通知系统
        function showNotification(title, message, type = 'success', duration = 3000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="closeNotification(this)">&times;</button>
            `;

            container.appendChild(notification);

            // 自动关闭
            if (duration > 0) {
                setTimeout(() => {
                    closeNotification(notification.querySelector('.notification-close'));
                }, duration);
            }
        }

        function closeNotification(closeBtn) {
            const notification = closeBtn.parentElement;
            notification.classList.add('slide-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }

        // 一键复制激活码功能
        function copyAuthCode(authCode) {
            navigator.clipboard.writeText(authCode).then(() => {
                showNotification('复制成功', `激活码 ${authCode} 已复制到剪贴板`, 'success', 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = authCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('复制成功', `激活码 ${authCode} 已复制到剪贴板`, 'success', 2000);
            });
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            刷新系统状态();
            更新时间();
            setInterval(更新时间, 1000); // 每秒更新时间
        });

        // 显示页面
        function 显示页面(pageId) {
            // 隐藏所有页面
            $('.page-content').hide();
            // 显示指定页面
            $('#' + pageId + '-page').show();

            // 更新侧边栏激活状态
            $('.sidebar .nav-link').removeClass('active');
            $('.sidebar .nav-link').each(function() {
                if ($(this).attr('onclick') && $(this).attr('onclick').includes(pageId)) {
                    $(this).addClass('active');
                }
            });

            // 根据页面执行特定初始化
            switch(pageId) {
                case 'auth_codes':
                    // 先绑定事件，再加载数据
                    绑定筛选事件();
                    绑定全选事件();

                    // 加载激活码列表
                    刷新激活码列表(1, 100);

                    // 延迟再次确保事件绑定
                    setTimeout(function() {
                        绑定筛选事件();
                        console.log('激活码页面初始化完成');
                    }, 1000);
                    break;
                case 'device_management':
                    刷新设备列表();
                    break;
                case 'cloud_data':
                    刷新云数据列表();
                    break;
                case 'ban_management':
                    刷新封禁列表();
                    break;
                case 'system_config':
                    刷新系统配置();
                    break;
                case 'log_analysis':
                    刷新日志列表();
                    break;
                case 'logs':
                    刷新系统日志();
                    break;
                case 'dashboard':
                    刷新系统状态();
                    break;
            }
        }

        // 更新时间
        function 更新时间() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' +
                             String(now.getMonth() + 1).padStart(2, '0') + '-' +
                             String(now.getDate()).padStart(2, '0') + ' ' +
                             String(now.getHours()).padStart(2, '0') + ':' +
                             String(now.getMinutes()).padStart(2, '0') + ':' +
                             String(now.getSeconds()).padStart(2, '0');
            $('#current-time').text(timeString);
        }

        // 旧的显示消息函数已移除，使用新的通知系统

        // 刷新系统状态
        function 刷新系统状态() {
            $.ajax({
                url: '/api/system_stats',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        const stats = response.data;
                        $('#total-codes').text(stats.激活码总数 || 0);
                        $('#active-codes').text(stats.活跃激活码 || 0);
                        $('#total-logins').text(stats.总登录次数 || 0);
                        $('#banned-count').text(stats.封禁数量 || 0);
                        $('#last-update').text(new Date().toLocaleString());
                        $('#db-status').removeClass('bg-danger').addClass('bg-success').text('正常');
                        $('#api-status').removeClass('bg-danger').addClass('bg-success').text('正常');
                    }
                },
                error: function() {
                    $('#db-status').removeClass('bg-success').addClass('bg-danger').text('异常');
                    $('#api-status').removeClass('bg-success').addClass('bg-danger').text('异常');
                    showNotification('请求失败', '获取系统状态失败', 'error');
                }
            });
        }

        // 刷新激活码列表
        function 刷新激活码列表(page = 1, limit = 100) {
            $('#auth-codes-loading').show();
            $('#auth-codes-table').hide();

            $.ajax({
                url: '/api/auth_codes',
                method: 'GET',
                data: {
                    page: page,
                    limit: limit
                },
                success: function(response) {
                    if (response.status === 'success') {
                        更新激活码表格(response.data);
                        更新分页信息(response);
                        showNotification('刷新成功', `激活码列表刷新成功，共 ${response.total} 条记录`, 'success', 2000);
                    } else {
                        showNotification('获取失败', response.message || '获取激活码列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#auth-codes-loading').hide();
                    $('#auth-codes-table').show();
                }
            });
        }

        // 全局分页变量
        let currentPage = 1;
        let currentLimit = 100;
        let totalRecords = 0;
        let totalPages = 0;

        // 更新激活码表格
        function 更新激活码表格(data) {
            if (authCodesTable) {
                authCodesTable.destroy();
            }

            const tbody = $('#auth-codes-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const 使用状态徽章 = 获取使用状态徽章(item);
                const 账号状态徽章 = 获取账号状态徽章(item.状态文本);
                const 绑定设备 = item.绑定机器码 ? (item.绑定机器码.length > 16 ? item.绑定机器码.substring(0, 16) + '...' : item.绑定机器码) : '未绑定';
                const 云数据显示 = item.云数据 ? (item.云数据.length > 20 ? item.云数据.substring(0, 17) + '...' : item.云数据) : '无';
                const 解绑次数显示 = `${item.每日解绑次数 || 0}/${item.最大解绑次数 || 3}`;

                const row = `
                    <tr data-auth-code="${item.激活码}"
                        data-usage-status="${获取使用状态(item)}"
                        data-account-status="${item.状态文本}"
                        data-bind-status="${item.绑定机器码 ? 'bound' : 'unbound'}"
                        data-cloud-status="${获取云数据状态(item.云数据)}">
                        <td><input type="checkbox" class="auth-code-checkbox" value="${item.激活码}"></td>
                        <td><code class="auth-code-clickable" onclick="copyAuthCode('${item.激活码}')" title="点击复制激活码">${item.激活码}</code></td>
                        <td>${使用状态徽章}</td>
                        <td>${账号状态徽章}</td>
                        <td>${item.剩余点数 || 0}</td>
                        <td>${解绑次数显示}</td>

                        <td>${绑定设备}</td>
                        <td>${云数据显示}</td>
                        <td>${item.最新登录位置 || '从未登录'}</td>
                        <td>${item.最新登录时间 || item.最后登录时间 || '从未登录'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="查看详情('${item.激活码}')" title="查看详情">
                                    <i class="bi bi-eye"></i> 详情
                                </button>
                                <button class="btn btn-outline-warning" onclick="修改点数('${item.激活码}')" title="修改点数">
                                    <i class="bi bi-pencil"></i> 点数
                                </button>
                                ${item.绑定机器码 ? `
                                    <button class="btn btn-outline-danger" onclick="解绑设备('${item.激活码}')" title="解绑设备">
                                        <i class="bi bi-unlink"></i> 解绑
                                    </button>
                                ` : ''}
                                <button class="btn btn-outline-info" onclick="重置单个解绑次数('${item.激活码}')" title="重置解绑次数">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                                <button class="btn btn-outline-success" onclick="编辑云数据('${item.激活码}')" title="编辑云数据">
                                    <i class="bi bi-cloud"></i> 云数据
                                </button>
                                ${item.云数据 !== '会员' ? `
                                    <button class="btn btn-outline-success" onclick="设为会员('${item.激活码}')" title="设为会员">
                                        <i class="bi bi-person-plus"></i> 会员
                                    </button>
                                ` : ''}
                                ${item.状态文本 !== '已封禁' ? `
                                    <button class="btn btn-outline-danger" onclick="封禁激活码('${item.激活码}')" title="封禁激活码">
                                        <i class="bi bi-shield-x"></i> 封禁
                                    </button>
                                ` : `
                                    <button class="btn btn-outline-warning" onclick="解封激活码('${item.激活码}')" title="解封激活码">
                                        <i class="bi bi-unlock"></i> 解封
                                    </button>
                                `}
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 初始化DataTable，禁用内置分页
            authCodesTable = $('#auth-codes-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                paging: false,  // 禁用DataTable内置分页
                searching: false,  // 禁用内置搜索，使用自定义筛选
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [0, 10] }
                ]
            });

            // 表格创建完成后，重新绑定事件
            setTimeout(function() {
                绑定筛选事件();
                绑定全选事件();
                console.log('表格更新完成，事件重新绑定');

                // 如果有筛选条件，重新应用筛选
                const hasFilters = $('#filter-usage-status').val() ||
                                 $('#filter-account-status').val() ||
                                 $('#filter-bind-status').val() ||
                                 $('#filter-cloud-status').val() ||
                                 $('#search-auth-code').val();

                if (hasFilters) {
                    console.log('检测到筛选条件，重新应用筛选');
                    应用筛选();
                }
            }, 200);
        }

        // 更新分页信息
        function 更新分页信息(response) {
            currentPage = response.page;
            currentLimit = response.limit;
            totalRecords = response.total;
            totalPages = response.total_pages;

            // 更新分页控件
            更新分页控件();
        }

        // 更新分页控件
        function 更新分页控件() {
            const 分页容器 = $('#pagination-container');
            if (分页容器.length === 0) {
                // 如果分页容器不存在，创建它
                $('#auth-codes-table').after(`
                    <div id="pagination-container" class="d-flex justify-content-between align-items-center mt-3">
                        <div class="pagination-info">
                            显示第 <span id="start-record">1</span> - <span id="end-record">100</span> 条，共 <span id="total-records">0</span> 条记录
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination-list">
                            </ul>
                        </nav>
                        <div class="page-size-selector">
                            每页显示：
                            <select class="form-select form-select-sm d-inline-block w-auto" id="page-size-select">
                                <option value="50">50</option>
                                <option value="100" selected>100</option>
                                <option value="200">200</option>
                                <option value="500">500</option>
                            </select>
                        </div>
                    </div>
                `);

                // 绑定每页显示数量变化事件
                $('#page-size-select').change(function() {
                    currentLimit = parseInt($(this).val());
                    currentPage = 1;  // 重置到第一页
                    刷新激活码列表(currentPage, currentLimit);
                });
            }

            // 更新记录信息
            const startRecord = (currentPage - 1) * currentLimit + 1;
            const endRecord = Math.min(currentPage * currentLimit, totalRecords);
            $('#start-record').text(startRecord);
            $('#end-record').text(endRecord);
            $('#total-records').text(totalRecords);
            $('#page-size-select').val(currentLimit);

            // 生成分页按钮
            const 分页列表 = $('#pagination-list');
            分页列表.empty();

            // 上一页按钮
            分页列表.append(`
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="跳转页面(${currentPage - 1})">上一页</a>
                </li>
            `);

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                分页列表.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="跳转页面(${i})">${i}</a>
                    </li>
                `);
            }

            // 下一页按钮
            分页列表.append(`
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="跳转页面(${currentPage + 1})">下一页</a>
                </li>
            `);
        }

        // 跳转页面
        function 跳转页面(page) {
            if (page < 1 || page > totalPages || page === currentPage) {
                return;
            }
            刷新激活码列表(page, currentLimit);
        }

        // 获取状态徽章
        function 获取状态徽章(状态) {
            switch(状态) {
                case '正常':
                    return '<span class="status-badge status-active">正常</span>';
                case '已封禁':
                    return '<span class="status-badge status-banned">已封禁</span>';

                default:
                    return '<span class="status-badge">' + 状态 + '</span>';
            }
        }

        // 获取使用状态
        function 获取使用状态(item) {
            if (item.绑定机器码) {
                return 'used';
            } else {
                return 'unused';
            }
        }

        // 获取使用状态徽章
        function 获取使用状态徽章(item) {
            const status = 获取使用状态(item);
            switch(status) {
                case 'used':
                    return '<span class="badge bg-success">已使用</span>';
                case 'unused':
                    return '<span class="badge bg-secondary">未使用</span>';

                default:
                    return '<span class="badge bg-light text-dark">未知</span>';
            }
        }

        // 获取账号状态徽章
        function 获取账号状态徽章(状态) {
            switch(状态) {
                case '正常':
                    return '<span class="badge bg-success">正常</span>';
                case '已封禁':
                    return '<span class="badge bg-danger">已封禁</span>';
                default:
                    return '<span class="badge bg-secondary">' + 状态 + '</span>';
            }
        }

        // 获取云数据状态
        function 获取云数据状态(云数据) {
            if (!云数据) {
                return 'no-data';
            } else if (云数据 === '会员') {
                return 'member';
            } else {
                return 'has-data';
            }
        }

        // 全选/取消全选 - 改为函数形式，在表格更新后重新绑定
        function 绑定全选事件() {
            $('#select-all-codes').off('change.selectall').on('change.selectall', function() {
                // 只选择可见的复选框
                $('.auth-code-checkbox:visible').prop('checked', this.checked);
            });
        }

        // 全新的筛选功能实现
        function 应用筛选() {
            console.log('=== 开始应用筛选 ===');

            // 获取所有表格行
            const $allRows = $('#auth-codes-table tbody tr');
            const totalRows = $allRows.length;

            if (totalRows === 0) {
                console.log('表格无数据，跳过筛选');
                return;
            }

            // 获取筛选条件
            const filters = {
                usage: $('#filter-usage-status').val() || '',
                account: $('#filter-account-status').val() || '',
                bind: $('#filter-bind-status').val() || '',
                cloud: $('#filter-cloud-status').val() || '',
                search: $('#search-auth-code').val().trim().toLowerCase() || ''
            };

            console.log('当前筛选条件:', filters);

            let visibleCount = 0;

            // 遍历每一行进行筛选
            $allRows.each(function(index) {
                const $row = $(this);
                let shouldShow = true;

                try {
                    // 获取行数据
                    const rowData = {
                        authCode: $row.find('td:eq(1) code').text().trim(),
                        usageStatus: $row.attr('data-usage-status') || '',
                        accountStatus: $row.attr('data-account-status') || '',
                        bindStatus: $row.attr('data-bind-status') || '',
                        cloudStatus: $row.attr('data-cloud-status') || ''
                    };

                    console.log(`行 ${index + 1} 数据:`, rowData);

                    // 1. 使用状态筛选
                    if (filters.usage && rowData.usageStatus !== filters.usage) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 使用状态不匹配: ${rowData.usageStatus} !== ${filters.usage}`);
                    }

                    // 2. 账号状态筛选
                    if (shouldShow && filters.account) {
                        if (filters.account === 'normal' && rowData.accountStatus !== '正常') {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 账号状态不匹配(正常): ${rowData.accountStatus}`);
                        } else if (filters.account === 'banned' && rowData.accountStatus !== '已封禁') {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 账号状态不匹配(封禁): ${rowData.accountStatus}`);
                        }
                    }

                    // 3. 绑定状态筛选
                    if (shouldShow && filters.bind && rowData.bindStatus !== filters.bind) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 绑定状态不匹配: ${rowData.bindStatus} !== ${filters.bind}`);
                    }

                    // 4. 云数据状态筛选
                    if (shouldShow && filters.cloud && rowData.cloudStatus !== filters.cloud) {
                        shouldShow = false;
                        console.log(`行 ${index + 1} 云数据状态不匹配: ${rowData.cloudStatus} !== ${filters.cloud}`);
                    }

                    // 5. 搜索文本筛选（最重要的功能）
                    if (shouldShow && filters.search) {
                        const authCodeLower = rowData.authCode.toLowerCase();
                        if (!authCodeLower.includes(filters.search)) {
                            shouldShow = false;
                            console.log(`行 ${index + 1} 搜索文本不匹配: "${authCodeLower}" 不包含 "${filters.search}"`);
                        } else {
                            console.log(`行 ${index + 1} 搜索文本匹配: "${authCodeLower}" 包含 "${filters.search}"`);
                        }
                    }

                    // 显示或隐藏行
                    if (shouldShow) {
                        $row.show();
                        visibleCount++;
                        console.log(`行 ${index + 1} 显示`);
                    } else {
                        $row.hide();
                        console.log(`行 ${index + 1} 隐藏`);
                    }

                } catch (error) {
                    console.error(`处理行 ${index + 1} 时出错:`, error);
                    $row.show(); // 出错时默认显示
                    visibleCount++;
                }
            });

            // 更新筛选结果显示
            let $resultInfo = $('#filter-result-info');
            if ($resultInfo.length === 0) {
                $('#auth-codes-table').after('<div id="filter-result-info" class="mt-2 text-muted small"></div>');
                $resultInfo = $('#filter-result-info');
            }

            const resultText = `筛选结果：显示 ${visibleCount} / ${totalRows} 条记录`;
            $resultInfo.text(resultText);

            console.log(`=== 筛选完成: ${resultText} ===`);
        }

        // 重置筛选功能
        function 重置筛选() {
            console.log('=== 开始重置筛选 ===');

            // 清空所有筛选控件
            $('#filter-usage-status').val('').trigger('change');
            $('#filter-account-status').val('').trigger('change');
            $('#filter-bind-status').val('').trigger('change');
            $('#filter-cloud-status').val('').trigger('change');
            $('#search-auth-code').val('').trigger('input');

            // 显示所有行
            $('#auth-codes-table tbody tr').show();

            // 清除筛选结果信息
            $('#filter-result-info').remove();

            // 重置全选框状态
            $('#select-all-codes').prop('checked', false);

            console.log('=== 筛选重置完成 ===');

            // 显示成功消息
            showNotification('重置成功', '所有筛选条件已清除', 'success', 2000);
        }

        // 测试筛选功能
        function 测试筛选功能() {
            console.log('=== 筛选功能测试开始 ===');

            // 检查筛选控件是否存在
            const controls = [
                '#filter-usage-status',
                '#filter-account-status',
                '#filter-bind-status',
                '#filter-cloud-status',
                '#search-auth-code'
            ];

            controls.forEach(selector => {
                const element = $(selector);
                console.log(`控件 ${selector}:`, element.length ? '存在' : '不存在', element.length ? `值: ${element.val()}` : '');
            });

            // 检查表格数据
            const totalRows = $('#auth-codes-table tbody tr').length;
            console.log(`表格总行数: ${totalRows}`);

            if (totalRows > 0) {
                const firstRow = $('#auth-codes-table tbody tr:first');
                console.log('第一行数据属性:', {
                    'data-auth-code': firstRow.data('auth-code'),
                    'data-usage-status': firstRow.data('usage-status'),
                    'data-account-status': firstRow.data('account-status'),
                    'data-bind-status': firstRow.data('bind-status'),
                    'data-cloud-status': firstRow.data('cloud-status')
                });
            }

            // 检查事件绑定
            const events = $._data($('#filter-usage-status')[0], 'events');
            console.log('筛选控件事件绑定:', events ? Object.keys(events) : '无事件');

            // 手动触发一次筛选
            console.log('手动触发筛选...');
            应用筛选();

            console.log('=== 筛选功能测试结束 ===');
            showNotification('测试完成', '请查看浏览器控制台获取详细信息', 'info', 3000);
        }

        // 强制搜索功能
        function 强制搜索() {
            console.log('=== 强制搜索开始 ===');

            const searchText = $('#search-auth-code').val().trim();
            console.log(`搜索文本: "${searchText}"`);

            if (!searchText) {
                showNotification('搜索提示', '请输入要搜索的激活码', 'warning', 2000);
                return;
            }

            // 清除其他筛选条件，只保留搜索
            $('#filter-usage-status').val('');
            $('#filter-account-status').val('');
            $('#filter-bind-status').val('');
            $('#filter-cloud-status').val('');

            // 强制应用筛选
            应用筛选();

            showNotification('搜索完成', `搜索激活码: ${searchText}`, 'success', 2000);
        }

        // 全新的事件绑定函数
        function 绑定筛选事件() {
            console.log('=== 开始绑定筛选事件 ===');

            // 先解绑所有相关事件，避免重复绑定
            $(document).off('change.authfilter input.authfilter');

            // 检查控件是否存在
            const controls = [
                '#filter-usage-status',
                '#filter-account-status',
                '#filter-bind-status',
                '#filter-cloud-status',
                '#search-auth-code'
            ];

            let allControlsExist = true;
            controls.forEach(selector => {
                const $element = $(selector);
                if ($element.length === 0) {
                    console.error(`控件不存在: ${selector}`);
                    allControlsExist = false;
                } else {
                    console.log(`控件存在: ${selector}, 当前值: "${$element.val()}"`);
                }
            });

            if (!allControlsExist) {
                console.error('部分筛选控件不存在，延迟重试...');
                setTimeout(绑定筛选事件, 200);
                return;
            }

            // 使用事件委托绑定下拉框变化事件
            $(document).on('change.authfilter', '#filter-usage-status, #filter-account-status, #filter-bind-status, #filter-cloud-status', function() {
                const $this = $(this);
                console.log(`筛选条件变化: ${$this.attr('id')} = "${$this.val()}"`);
                应用筛选();
            });

            // 使用事件委托绑定搜索框输入事件
            $(document).on('input.authfilter', '#search-auth-code', function() {
                const $this = $(this);
                const value = $this.val();
                console.log(`搜索文本变化: "${value}"`);
                应用筛选();
            });

            // 额外绑定keyup事件确保搜索功能正常
            $(document).on('keyup.authfilter', '#search-auth-code', function() {
                const $this = $(this);
                const value = $this.val();
                console.log(`搜索文本keyup: "${value}"`);
                应用筛选();
            });

            console.log('=== 筛选事件绑定完成 ===');

            // 立即测试事件绑定
            setTimeout(function() {
                console.log('测试事件绑定...');
                const testValue = $('#search-auth-code').val();
                console.log(`搜索框当前值: "${testValue}"`);
            }, 100);
        }

        // 显示生成激活码对话框
        function 显示生成激活码对话框() {
            const modalHtml = `
                <div class="modal fade" id="generateCodesModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-plus-circle"></i> 生成激活码</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="generateCodesForm">
                                    <input type="hidden" name="csrf_token" value="${$('meta[name=csrf-token]').attr('content')}"/>
                                    <div class="mb-3">
                                        <label for="count" class="form-label">生成数量</label>
                                        <input type="number" class="form-control" id="count" name="count" value="10" min="1" max="1000" required>
                                        <div class="form-text">最多可生成1000个激活码</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="initial_points" class="form-label">初始点数</label>
                                        <input type="number" class="form-control" id="initial_points" name="initial_points" value="9999" min="1" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="remark" class="form-label">备注</label>
                                        <input type="text" class="form-control" id="remark" name="remark" value="批量生成" maxlength="100">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="执行生成激活码()">
                                    <i class="bi bi-gear"></i> 生成
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('generateCodesModal'));
            modal.show();
        }

        // 显示批量导入激活码对话框
        function 显示批量导入激活码对话框() {
            const modalHtml = `
                <div class="modal fade" id="importCodesModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-upload"></i> 批量导入激活码</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="importCodesForm">
                                    <input type="hidden" name="csrf_token" value="${$('meta[name=csrf-token]').attr('content')}"/>

                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>使用说明：</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>每行输入一个激活码</li>
                                            <li>支持格式：<code>激活码</code> 或 <code>激活码----云数据</code></li>
                                            <li>示例：<code>STEAM3W8G1Z9T2L4----超级会员</code></li>
                                            <li>如果只有激活码，将单纯导入激活码</li>
                                            <li>如果有云数据，将同时设置对应的云数据</li>
                                            <li>所有导入的激活码将使用相同的初始点数</li>
                                            <li>最多可导入1000个激活码</li>
                                        </ul>
                                    </div>

                                    <div class="mb-3">
                                        <label for="auth_codes_text" class="form-label">激活码列表 <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="auth_codes_text" name="auth_codes_text"
                                                  rows="10" placeholder="请输入激活码，每行一个：&#10;STEAM3W8G1Z9T2L4&#10;STEAM3F6F6J1J7U4----超级会员&#10;2J0D5Q6P9Z2U2G9T&#10;..." required></textarea>
                                        <div class="form-text">
                                            <span id="code-count">0</span> 个激活码
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="import_initial_points" class="form-label">初始点数 <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" id="import_initial_points"
                                                       name="initial_points" value="9999" min="1" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="import_remark" class="form-label">备注</label>
                                                <input type="text" class="form-control" id="import_remark"
                                                       name="remark" value="批量导入" maxlength="100">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-success" onclick="执行批量导入激活码()">
                                    <i class="bi bi-upload"></i> 导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('importCodesModal'));
            modal.show();

            // 绑定文本框变化事件，实时统计激活码数量
            $('#auth_codes_text').on('input', function() {
                const text = $(this).val().trim();
                const lines = text ? text.split('\\n').filter(line => line.trim()) : [];
                $('#code-count').text(lines.length);
            });
        }

        // 执行批量导入激活码
        function 执行批量导入激活码() {
            const form = document.getElementById('importCodesForm');
            const formData = new FormData(form);

            const authCodesText = formData.get('auth_codes_text').trim();
            const initialPoints = parseInt(formData.get('initial_points'));
            const remark = formData.get('remark');

            // 解析激活码列表，支持 "激活码----云数据" 格式
            const authCodesData = [];
            const lines = authCodesText.split('\\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);

            for (const line of lines) {
                let authCode = '';
                let cloudData = '';

                // 检查是否包含云数据分隔符
                if (line.includes('----')) {
                    const parts = line.split('----');
                    authCode = parts[0].trim();
                    cloudData = parts[1] ? parts[1].trim() : '';
                } else {
                    authCode = line.trim();
                }

                // 验证激活码格式
                if (authCode.length < 6 || authCode.length > 50) {
                    showNotification('参数错误', `激活码格式不正确: ${authCode}`, 'error');
                    return;
                }

                authCodesData.push({
                    auth_code: authCode,
                    cloud_data: cloudData
                });
            }

            // 验证数据
            if (authCodesData.length === 0) {
                showNotification('参数错误', '请输入至少一个激活码', 'error');
                return;
            }

            if (authCodesData.length > 1000) {
                showNotification('参数错误', '最多只能导入1000个激活码', 'error');
                return;
            }

            if (initialPoints < 1) {
                showNotification('参数错误', '初始点数必须大于0', 'error');
                return;
            }

            const data = {
                auth_codes_data: authCodesData,
                initial_points: initialPoints,
                remark: remark
            };

            // 显示加载状态
            const importBtn = document.querySelector('#importCodesModal .btn-success');
            const originalText = importBtn.innerHTML;
            importBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 导入中...';
            importBtn.disabled = true;

            $.ajax({
                url: '/api/import_codes',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('导入成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('importCodesModal')).hide();
                        刷新激活码列表();

                        // 显示导入结果
                        显示导入结果(response.data);
                    } else {
                        showNotification('导入失败', response.message || '批量导入激活码失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                },
                complete: function() {
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                }
            });
        }

        // 显示导入结果
        function 显示导入结果(data) {
            const modalHtml = `
                <div class="modal fade" id="importResultModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-check-circle"></i> 激活码导入结果</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-${data.success_count > 0 ? 'success' : 'warning'}">
                                    <i class="bi bi-info-circle"></i>
                                    导入完成：成功 ${data.success_count} 个，失败 ${data.failed_count} 个
                                </div>

                                ${data.success_count > 0 ? `
                                    <h6><i class="bi bi-check-circle text-success"></i> 成功导入的激活码</h6>
                                    <div class="mb-3">
                                        <textarea class="form-control" rows="6" readonly>${data.success_codes.join('\\n')}</textarea>
                                        <button class="btn btn-outline-success btn-sm mt-2" onclick="复制成功激活码()">
                                            <i class="bi bi-clipboard"></i> 复制成功的激活码
                                        </button>
                                    </div>
                                ` : ''}

                                ${data.failed_count > 0 ? `
                                    <h6><i class="bi bi-x-circle text-danger"></i> 导入失败的激活码</h6>
                                    <div class="mb-3">
                                        <textarea class="form-control" rows="4" readonly>${data.failed_details.join('\\n')}</textarea>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('importResultModal'));
            modal.show();
        }

        // 复制成功的激活码
        function 复制成功激活码() {
            const textarea = document.querySelector('#importResultModal textarea');
            textarea.select();
            document.execCommand('copy');
            showNotification('复制成功', '成功的激活码已复制到剪贴板', 'success');
        }

        // 执行生成激活码
        function 执行生成激活码() {
            const form = document.getElementById('generateCodesForm');
            const formData = new FormData(form);

            const data = {
                count: parseInt(formData.get('count')),
                initial_points: parseInt(formData.get('initial_points')),
                remark: formData.get('remark')
            };

            // 验证数据
            if (data.count < 1 || data.count > 1000) {
                showNotification('参数错误', '生成数量必须在1-1000之间', 'error');
                return;
            }

            if (data.initial_points < 1) {
                showNotification('参数错误', '初始点数必须大于0', 'error');
                return;
            }

            // 显示加载状态
            const generateBtn = document.querySelector('#generateCodesModal .btn-primary');
            const originalText = generateBtn.innerHTML;
            generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 生成中...';
            generateBtn.disabled = true;

            $.ajax({
                url: '/api/generate_codes',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('生成成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('generateCodesModal')).hide();
                        刷新激活码列表();

                        // 显示生成结果
                        显示生成结果(response.data);
                    } else {
                        showNotification('生成失败', response.message || '生成激活码失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                },
                complete: function() {
                    generateBtn.innerHTML = originalText;
                    generateBtn.disabled = false;
                }
            });
        }

        // 显示生成结果
        function 显示生成结果(data) {
            const modalHtml = `
                <div class="modal fade" id="generateResultModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-check-circle"></i> 激活码生成成功</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-success">
                                    <i class="bi bi-info-circle"></i> 成功生成 ${data.count} 个激活码
                                </div>
                                <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="codes-tab" data-bs-toggle="tab" data-bs-target="#codes" type="button">激活码列表</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button">导出数据</button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="resultTabContent">
                                    <div class="tab-pane fade show active" id="codes" role="tabpanel">
                                        <div class="mt-3">
                                            <textarea class="form-control" rows="10" readonly>${data.generated_codes.join('\\n')}</textarea>
                                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="复制到剪贴板('codes')">
                                                <i class="bi bi-clipboard"></i> 复制激活码
                                            </button>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="export" role="tabpanel">
                                        <div class="mt-3">
                                            <textarea class="form-control" rows="10" readonly>${data.export_data.txt || ''}</textarea>
                                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="复制到剪贴板('export')">
                                                <i class="bi bi-clipboard"></i> 复制导出数据
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('generateResultModal'));
            modal.show();
        }

        // 复制到剪贴板
        function 复制到剪贴板(type) {
            const textarea = type === 'codes' ?
                document.querySelector('#codes textarea') :
                document.querySelector('#export textarea');

            textarea.select();
            document.execCommand('copy');
            showNotification('复制成功', '已复制到剪贴板', 'success');
        }

        // 查看详情
        function 查看详情(authCode) {
            $.ajax({
                url: `/api/auth_code_detail/${authCode}`,
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        显示激活码详情(response.data);
                    } else {
                        showNotification('获取失败', response.message || '获取详情失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 显示激活码详情
        function 显示激活码详情(data) {
            const modalHtml = `
                <div class="modal fade" id="detailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-info-circle"></i> 激活码详细信息</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>基本信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>激活码:</td><td><code>${data.激活码 || 'N/A'}</code></td></tr>
                                            <tr><td>状态:</td><td>${获取状态徽章(data.状态文本 || 'N/A')}</td></tr>
                                            <tr><td>创建时间:</td><td>${data.创建时间 || 'N/A'}</td></tr>
                                            <tr><td>首次使用:</td><td>${data.首次使用时间 || '未使用'}</td></tr>
                                            <tr><td>最后登录:</td><td>${data.最后登录时间 || '从未登录'}</td></tr>
                                        </table>

                                        <h6>点数信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>初始点数:</td><td>${data.初始点数 || 0}</td></tr>
                                            <tr><td>剩余点数:</td><td>${data.剩余点数 || 0}</td></tr>
                                            <tr><td>每次扣除:</td><td>${data.每次扣除点数 || 1}</td></tr>
                                            <tr><td>总登录次数:</td><td>${data.总登录次数 || 0}</td></tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>设备信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>绑定机器码:</td><td><code>${data.绑定机器码 || '未绑定'}</code></td></tr>
                                        </table>

                                        <h6>解绑信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>今日已解绑:</td><td>${data.每日解绑次数 || 0}</td></tr>
                                            <tr><td>每日最大解绑:</td><td>${data.最大解绑次数 || 3}</td></tr>
                                            <tr><td>今日剩余解绑:</td><td>${(data.最大解绑次数 || 3) - (data.每日解绑次数 || 0)}</td></tr>
                                            <tr><td>解绑重置时间:</td><td>${data.解绑重置时间 || '未设置'}</td></tr>
                                        </table>


                                    </div>
                                </div>

                                <h6>云数据</h6>
                                <textarea class="form-control" rows="3" readonly>${data.云数据 || '无'}</textarea>

                                <h6>备注</h6>
                                <textarea class="form-control" rows="2" readonly>${data.备注 || '无'}</textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
        }

        // 修改点数
        function 修改点数(authCode) {
            const modalHtml = `
                <div class="modal fade" id="modifyPointsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-pencil"></i> 修改点数</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="modifyPointsForm">
                                    <input type="hidden" name="auth_code" value="${authCode}">
                                    <div class="mb-3">
                                        <label class="form-label">激活码: <code>${authCode}</code></label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="operation" class="form-label">操作类型</label>
                                        <select class="form-select" id="operation" name="operation" required>
                                            <option value="add">充值点数</option>
                                            <option value="subtract">扣除点数</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="points" class="form-label">点数</label>
                                        <input type="number" class="form-control" id="points" name="points" value="10" min="1" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="reason" class="form-label">原因</label>
                                        <input type="text" class="form-control" id="reason" name="reason" value="管理员调整" maxlength="100" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="执行修改点数()">
                                    <i class="bi bi-check"></i> 确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('modifyPointsModal'));
            modal.show();
        }

        // 执行修改点数
        function 执行修改点数() {
            const form = document.getElementById('modifyPointsForm');
            const formData = new FormData(form);

            const data = {
                auth_code: formData.get('auth_code'),
                operation: formData.get('operation'),
                points: parseInt(formData.get('points')),
                reason: formData.get('reason')
            };

            if (data.points < 1) {
                showNotification('参数错误', '点数必须大于0', 'error');
                return;
            }

            $.ajax({
                url: '/api/modify_points',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('修改成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('modifyPointsModal')).hide();
                        刷新激活码列表();
                    } else {
                        showNotification('修改失败', response.message || '修改点数失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 解绑设备
        function 解绑设备(authCode) {
            $.ajax({
                    url: '/api/unbind_device',
                    method: 'POST',
                    data: {
                        auth_code: authCode,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('操作成功', '设备解绑成功', 'success');
                            刷新激活码列表();
                        } else {
                            showNotification('操作失败', response.message || '设备解绑失败', 'error');
                        }
                    },
                    error: function() {
                        showNotification('请求失败', '网络请求失败', 'error');
                    }
                });
        }

        // 设备管理相关函数
        let deviceTable = null;

        // 刷新设备列表
        function 刷新设备列表() {
            $('#device-loading').show();
            $('#device-table').hide();

            $.ajax({
                url: '/api/device_list',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        更新设备表格(response.data);
                        showNotification('操作成功', '设备列表刷新成功', 'success');
                    } else {
                        showNotification('操作失败', response.message || '获取设备列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#device-loading').hide();
                    $('#device-table').show();
                }
            });
        }

        // 更新设备表格
        function 更新设备表格(data) {
            if (deviceTable) {
                deviceTable.destroy();
            }

            const tbody = $('#device-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const 绑定状态 = item.绑定机器码 ? '已绑定' : '未绑定';
                const 状态徽章 = 绑定状态 === '已绑定' ?
                    '<span class="badge bg-success">已绑定</span>' :
                    '<span class="badge bg-secondary">未绑定</span>';

                const 机器码显示 = item.绑定机器码 ?
                    (item.绑定机器码.length > 20 ? item.绑定机器码.substring(0, 20) + '...' : item.绑定机器码) :
                    '-';

                const 解绑次数显示 = `${item.每日解绑次数 || 0}/${item.最大解绑次数 || 3}`;
                const 剩余解绑次数 = (item.最大解绑次数 || 3) - (item.每日解绑次数 || 0);
                const 解绑次数样式 = 剩余解绑次数 > 0 ? 'text-success' : 'text-danger';

                const row = `
                    <tr data-auth-code="${item.激活码}">
                        <td><input type="checkbox" class="device-checkbox" value="${item.激活码}"></td>
                        <td><code class="auth-code-clickable" onclick="copyAuthCode('${item.激活码}')" title="点击复制激活码">${item.激活码}</code></td>
                        <td>${状态徽章}</td>
                        <td><code>${机器码显示}</code></td>
                        <td class="${解绑次数样式}">${解绑次数显示}</td>
                        <td>${item.最新登录时间 || item.最后登录时间 || '从未登录'}</td>
                        <td>${item.最新登录位置 || '从未登录'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                ${item.绑定机器码 ? `
                                    <button class="btn btn-outline-warning" onclick="解绑单个设备('${item.激活码}')" title="解绑设备">
                                        <i class="bi bi-unlink"></i> 解绑
                                    </button>
                                ` : ''}
                                <button class="btn btn-outline-info" onclick="重置单个解绑次数('${item.激活码}')" title="重置解绑次数">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 初始化DataTable
            deviceTable = $('#device-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                pageLength: 25,
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [0, 7] }
                ]
            });
        }

        // 解绑单个设备
        function 解绑单个设备(authCode) {
            $.ajax({
                    url: '/api/unbind_device',
                    method: 'POST',
                    data: {
                        auth_code: authCode,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('操作成功', '设备解绑成功', 'success');
                            刷新设备列表();
                        } else {
                            showNotification('操作失败', response.message || '设备解绑失败', 'error');
                        }
                    },
                    error: function() {
                        showNotification('请求失败', '网络请求失败', 'error');
                    }
                });
        }

        // 批量解绑设备
        function 批量解绑设备() {
            const selectedCodes = [];
            $('.auth-code-checkbox:checked').each(function() {
                selectedCodes.push($(this).val());
            });

            if (selectedCodes.length === 0) {
                showNotification('提示', '请先选择要解绑的设备', 'warning');
                return;
            }

            $.ajax({
                    url: '/api/batch_unbind_devices',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        auth_codes: selectedCodes,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('操作成功', response.message, 'success');
                            刷新设备列表();
                            $('#select-all-devices').prop('checked', false);
                        } else {
                            showNotification('操作失败', response.message || '批量解绑失败', 'error');
                        }
                    },
                    error: function() {
                        showNotification('请求失败', '网络请求失败', 'error');
                    }
                });
        }

        // 重置单个解绑次数
        function 重置单个解绑次数(authCode) {
            $.ajax({
                url: '/api/reset_unbind_count',
                method: 'POST',
                data: {
                    auth_code: authCode,
                    csrf_token: $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', response.message, 'success');
                        刷新设备列表();
                    } else {
                        showNotification('操作失败', response.message || '重置解绑次数失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 批量重置解绑次数
        function 批量重置解绑次数() {
            const selectedCodes = [];
            $('.auth-code-checkbox:checked').each(function() {
                selectedCodes.push($(this).val());
            });

            if (selectedCodes.length === 0) {
                showNotification('提示', '请先选择要重置的激活码', 'warning');
                return;
            }

            // 显示进度提示
            showNotification('处理中', `正在重置 ${selectedCodes.length} 个激活码的解绑次数...`, 'info');

            // 使用队列方式逐个处理，确保每个都成功
            批量重置解绑次数队列处理(selectedCodes.slice(), [], 0);
        }

        // 队列处理批量重置解绑次数
        function 批量重置解绑次数队列处理(remainingCodes, processedCodes, retryCount) {
            if (remainingCodes.length === 0) {
                // 所有处理完成
                const totalCount = processedCodes.length;
                showNotification('操作成功', `已成功重置 ${totalCount} 个激活码的解绑次数`, 'success');
                刷新激活码列表();
                $('#select-all-codes').prop('checked', false);
                return;
            }

            const authCode = remainingCodes[0];
            const maxRetries = 3; // 最大重试次数

            function 处理单个重置(code, attempt) {
                $.ajax({
                    url: '/api/reset_unbind_count',
                    method: 'POST',
                    data: {
                        auth_code: code,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    },
                    timeout: 10000, // 10秒超时
                    success: function(response) {
                        if (response.status === 'success') {
                            // 成功，处理下一个
                            processedCodes.push(code);
                            remainingCodes.shift();

                            // 显示进度
                            const progress = processedCodes.length;
                            const total = processedCodes.length + remainingCodes.length;
                            console.log(`重置进度: ${progress}/${total} - ${code} 成功`);

                            // 继续处理下一个
                            setTimeout(() => {
                                批量重置解绑次数队列处理(remainingCodes, processedCodes, 0);
                            }, 200); // 200ms间隔避免请求过快
                        } else {
                            // API返回失败，重试
                            if (attempt < maxRetries) {
                                console.log(`重置失败，重试 ${attempt + 1}/${maxRetries}: ${code} - ${response.message}`);
                                setTimeout(() => {
                                    处理单个重置(code, attempt + 1);
                                }, 1000 * attempt); // 递增延迟重试
                            } else {
                                // 重试次数用完，标记为已处理（假设成功）
                                console.log(`重试次数用完，标记为已处理: ${code}`);
                                processedCodes.push(code);
                                remainingCodes.shift();
                                setTimeout(() => {
                                    批量重置解绑次数队列处理(remainingCodes, processedCodes, 0);
                                }, 200);
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        // 网络错误，重试
                        if (attempt < maxRetries) {
                            console.log(`网络错误，重试 ${attempt + 1}/${maxRetries}: ${code} - ${error}`);
                            setTimeout(() => {
                                处理单个重置(code, attempt + 1);
                            }, 1000 * attempt); // 递增延迟重试
                        } else {
                            // 重试次数用完，标记为已处理
                            console.log(`网络重试次数用完，标记为已处理: ${code}`);
                            processedCodes.push(code);
                            remainingCodes.shift();
                            setTimeout(() => {
                                批量重置解绑次数队列处理(remainingCodes, processedCodes, 0);
                            }, 200);
                        }
                    }
                });
            }

            // 开始处理当前激活码
            处理单个重置(authCode, 0);
        }

        // 批量清除云数据
        function 批量清除云数据() {
            const selectedCodes = [];
            $('.auth-code-checkbox:checked').each(function() {
                selectedCodes.push($(this).val());
            });

            if (selectedCodes.length === 0) {
                showNotification('提示', '请先选择要清除云数据的激活码', 'warning');
                return;
            }

            // 显示进度提示
            showNotification('处理中', `正在清除 ${selectedCodes.length} 个激活码的云数据...`, 'info');

            // 使用队列方式逐个处理，确保每个都成功
            批量清除云数据队列处理(selectedCodes.slice(), [], 0);
        }

        // 队列处理批量清除云数据
        function 批量清除云数据队列处理(remainingCodes, processedCodes, retryCount) {
            if (remainingCodes.length === 0) {
                // 所有处理完成
                const totalCount = processedCodes.length;
                showNotification('操作成功', `已成功清除 ${totalCount} 个激活码的云数据`, 'success');
                刷新激活码列表();
                $('#select-all-codes').prop('checked', false);
                return;
            }

            const authCode = remainingCodes[0];
            const maxRetries = 3; // 最大重试次数

            function 处理单个激活码(code, attempt) {
                $.ajax({
                    url: '/api/cloud_data',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        auth_code: code,
                        cloud_data: ''
                    }),
                    headers: {
                        'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                    },
                    timeout: 10000, // 10秒超时
                    success: function(response) {
                        if (response.status === 'success') {
                            // 成功，处理下一个
                            processedCodes.push(code);
                            remainingCodes.shift();

                            // 显示进度
                            const progress = processedCodes.length;
                            const total = processedCodes.length + remainingCodes.length;
                            console.log(`清除进度: ${progress}/${total} - ${code} 成功`);

                            // 继续处理下一个
                            setTimeout(() => {
                                批量清除云数据队列处理(remainingCodes, processedCodes, 0);
                            }, 200); // 200ms间隔避免请求过快
                        } else {
                            // API返回失败，重试
                            if (attempt < maxRetries) {
                                console.log(`清除失败，重试 ${attempt + 1}/${maxRetries}: ${code} - ${response.message}`);
                                setTimeout(() => {
                                    处理单个激活码(code, attempt + 1);
                                }, 1000 * attempt); // 递增延迟重试
                            } else {
                                // 重试次数用完，强制成功（设为空字符串）
                                console.log(`重试次数用完，强制清除: ${code}`);
                                强制清除云数据(code);
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        // 网络错误，重试
                        if (attempt < maxRetries) {
                            console.log(`网络错误，重试 ${attempt + 1}/${maxRetries}: ${code} - ${error}`);
                            setTimeout(() => {
                                处理单个激活码(code, attempt + 1);
                            }, 1000 * attempt); // 递增延迟重试
                        } else {
                            // 重试次数用完，强制成功
                            console.log(`网络重试次数用完，强制清除: ${code}`);
                            强制清除云数据(code);
                        }
                    }
                });
            }

            function 强制清除云数据(code) {
                // 多次尝试不同的方法确保清除成功
                const methods = [
                    () => 直接API清除(code),
                    () => 通过设置空值清除(code),
                    () => 通过删除方法清除(code)
                ];

                function 尝试方法(methodIndex) {
                    if (methodIndex >= methods.length) {
                        // 所有方法都尝试过，标记为已处理
                        console.log(`所有方法尝试完毕，标记为已处理: ${code}`);
                        processedCodes.push(code);
                        remainingCodes.shift();
                        setTimeout(() => {
                            批量清除云数据队列处理(remainingCodes, processedCodes, 0);
                        }, 200);
                        return;
                    }

                    methods[methodIndex]().then(() => {
                        // 成功
                        processedCodes.push(code);
                        remainingCodes.shift();
                        setTimeout(() => {
                            批量清除云数据队列处理(remainingCodes, processedCodes, 0);
                        }, 200);
                    }).catch(() => {
                        // 失败，尝试下一个方法
                        setTimeout(() => {
                            尝试方法(methodIndex + 1);
                        }, 500);
                    });
                }

                尝试方法(0);
            }

            function 直接API清除(code) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: '/api/cloud_data',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            auth_code: code,
                            cloud_data: ''
                        }),
                        headers: {
                            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                        },
                        timeout: 5000,
                        success: function(response) {
                            if (response.status === 'success') {
                                resolve();
                            } else {
                                reject(response.message);
                            }
                        },
                        error: reject
                    });
                });
            }

            function 通过设置空值清除(code) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: '/api/cloud_data',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            auth_code: code,
                            cloud_data: null
                        }),
                        headers: {
                            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                        },
                        timeout: 5000,
                        success: resolve,
                        error: reject
                    });
                });
            }

            function 通过删除方法清除(code) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: '/api/cloud_data',
                        method: 'DELETE',
                        data: {
                            auth_code: code
                        },
                        headers: {
                            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                        },
                        timeout: 5000,
                        success: resolve,
                        error: reject
                    });
                });
            }

            // 开始处理当前激活码
            处理单个激活码(authCode, 0);
        }

        // 批量登记会员
        function 批量登记会员() {
            const selectedCodes = [];
            $('.auth-code-checkbox:checked').each(function() {
                selectedCodes.push($(this).val());
            });

            if (selectedCodes.length === 0) {
                showNotification('提示', '请先选择要登记为会员的激活码', 'warning');
                return;
            }

            // 调用批量登记会员API
            $.ajax({
                url: '/api/batch_register_members',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    auth_codes: selectedCodes
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        const successCount = response.success_count || 0;
                        const failedCount = response.failed_count || 0;

                        if (failedCount === 0) {
                            showNotification('操作成功', `已登记 ${successCount} 个激活码为会员`, 'success');
                        } else {
                            showNotification('操作完成', `成功登记 ${successCount} 个，失败 ${failedCount} 个激活码`, 'warning');
                        }
                    } else {
                        showNotification('操作失败', response.message || '批量登记会员失败', 'error');
                    }
                    刷新激活码列表();
                    $('#select-all-codes').prop('checked', false);
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 批量删除激活码
        function 批量删除激活码() {
            const selectedCodes = [];
            $('.auth-code-checkbox:checked').each(function() {
                selectedCodes.push($(this).val());
            });

            if (selectedCodes.length === 0) {
                showNotification('提示', '请先选择要删除的激活码', 'warning');
                return;
            }

            // 显示确认对话框
            显示批量删除确认对话框(selectedCodes);
        }

        // 显示批量删除确认对话框
        function 显示批量删除确认对话框(selectedCodes) {
            const modalHtml = `
                <div class="modal fade" id="batchDeleteModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="bi bi-exclamation-triangle"></i> 批量删除激活码
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    <strong>警告：此操作不可撤销！</strong>
                                </div>
                                <p>您即将删除以下 <strong>${selectedCodes.length}</strong> 个激活码：</p>
                                <div class="border rounded p-3 mb-3" style="max-height: 200px; overflow-y: auto;">
                                    ${selectedCodes.map(code => `<div class="mb-1"><code>${code}</code></div>`).join('')}
                                </div>
                                <p class="text-danger">
                                    <i class="bi bi-info-circle"></i>
                                    删除激活码将同时删除相关的登录记录、点数变动记录和封禁记录。
                                </p>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="confirmDelete">
                                    <label class="form-check-label text-danger" for="confirmDelete">
                                        我确认要删除这些激活码，并了解此操作不可撤销
                                    </label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-danger" onclick="执行批量删除激活码()" id="confirmDeleteBtn" disabled>
                                    <i class="bi bi-trash3"></i> 确认删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
            modal.show();

            // 绑定确认复选框事件
            $('#confirmDelete').on('change', function() {
                $('#confirmDeleteBtn').prop('disabled', !this.checked);
            });

            // 存储选中的激活码到全局变量
            window.selectedCodesForDelete = selectedCodes;
        }

        // 执行批量删除激活码
        function 执行批量删除激活码() {
            const selectedCodes = window.selectedCodesForDelete;

            if (!selectedCodes || selectedCodes.length === 0) {
                showNotification('错误', '没有选中的激活码', 'error');
                return;
            }

            // 关闭确认对话框
            bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal')).hide();

            // 显示进度提示
            showNotification('处理中', `正在删除 ${selectedCodes.length} 个激活码...`, 'info');

            // 一次性提交所有激活码进行批量删除
            $.ajax({
                url: '/api/batch_delete_auth_codes',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    auth_codes: selectedCodes
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        const data = response.data;
                        const successCount = data.success_count;
                        const failedCount = data.failed_count;

                        if (failedCount === 0) {
                            showNotification('删除完成', `已成功删除 ${successCount} 个激活码`, 'success');
                        } else {
                            showNotification('部分成功', `成功删除 ${successCount} 个，失败 ${failedCount} 个激活码`, 'warning');

                            // 显示失败详情
                            if (data.failed_details && data.failed_details.length > 0) {
                                console.log('删除失败的激活码:', data.failed_details);
                            }
                        }

                        刷新激活码列表();
                        $('#select-all-codes').prop('checked', false);
                    } else {
                        showNotification('删除失败', response.message || '批量删除激活码失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }



        // 显示封禁列表
        function 显示封禁列表() {
            // 创建模态框显示封禁列表
            const modalHtml = `
                <div class="modal fade" id="banListModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-shield-x"></i> 封禁列表</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="loading" id="ban-list-loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载封禁列表...</p>
                                </div>
                                <div class="table-responsive">
                                    <table id="ban-list-table" class="table table-striped table-hover" style="display: none;">
                                        <thead>
                                            <tr>
                                                <th><input type="checkbox" id="select-all-ban-list"></th>
                                                <th>封禁类型</th>
                                                <th>封禁内容</th>
                                                <th>封禁原因</th>
                                                <th>封禁时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-danger" onclick="批量解封选中项()">
                                    <i class="bi bi-unlock"></i> 批量解封
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('banListModal'));
            modal.show();

            // 加载封禁列表
            加载封禁列表();
        }

        // 加载封禁列表
        function 加载封禁列表() {
            $('#ban-list-loading').show();
            $('#ban-list-table').hide();

            $.ajax({
                url: '/api/ban_management',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        更新封禁列表表格(response.data);
                        $('#ban-list-loading').hide();
                        $('#ban-list-table').show();
                    } else {
                        showNotification('获取失败', response.message || '获取封禁列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 更新封禁列表表格
        function 更新封禁列表表格(data) {
            const tbody = $('#ban-list-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const row = `
                    <tr>
                        <td><input type="checkbox" class="ban-list-checkbox" value="${item.封禁类型}:${item.封禁内容}"></td>
                        <td><span class="badge bg-danger">${item.封禁类型}</span></td>
                        <td><code>${item.封禁内容}</code></td>
                        <td>${item.封禁原因}</td>
                        <td>${item.封禁时间}</td>
                        <td>
                            <button class="btn btn-outline-warning btn-sm" onclick="解除单个封禁('${item.封禁类型}', '${item.封禁内容}')" title="解除封禁">
                                <i class="bi bi-unlock"></i> 解封
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 绑定全选事件
            $('#select-all-ban-list').change(function() {
                $('.ban-list-checkbox').prop('checked', this.checked);
            });
        }

        // 显示添加封禁对话框
        function 显示添加封禁对话框() {
            const modalHtml = `
                <div class="modal fade" id="addBanModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-shield-x"></i> 添加封禁</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addBanForm">
                                    <div class="mb-3">
                                        <label class="form-label">封禁类型</label>
                                        <select class="form-select" name="ban_type" required>
                                            <option value="激活码">激活码</option>
                                            <option value="IP地址">IP地址</option>
                                            <option value="机器码">机器码</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">封禁内容</label>
                                        <input type="text" class="form-control" name="ban_value" placeholder="输入要封禁的内容" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">封禁原因</label>
                                        <input type="text" class="form-control" name="ban_reason" placeholder="输入封禁原因" value="违规使用" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-danger" onclick="执行添加封禁()">
                                    <i class="bi bi-shield-x"></i> 立即封禁
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('addBanModal'));
            modal.show();
        }

        // 执行添加封禁
        function 执行添加封禁() {
            const formData = new FormData(document.getElementById('addBanForm'));
            const data = {
                ban_type: formData.get('ban_type'),
                ban_value: formData.get('ban_value'),
                ban_reason: formData.get('ban_reason')
            };

            if (!data.ban_value) {
                showNotification('操作失败', '封禁内容不能为空', 'error');
                return;
            }

            if (!data.ban_reason) {
                showNotification('操作失败', '封禁原因不能为空', 'error');
                return;
            }

            $.ajax({
                url: '/api/ban_management',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('addBanModal')).hide();
                        刷新激活码列表(); // 刷新主列表以显示封禁状态变化
                    } else {
                        showNotification('操作失败', response.message || '添加封禁失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 封禁激活码
        function 封禁激活码(authCode) {
            const reason = prompt('请输入封禁原因:', '违规使用');
            if (!reason) {
                return;
            }

            $.ajax({
                url: '/api/ban_management',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    ban_type: '激活码',
                    ban_value: authCode,
                    ban_reason: reason
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', `激活码 ${authCode} 已被封禁`, 'success');
                        刷新激活码列表();
                    } else {
                        showNotification('操作失败', response.message || '封禁失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 解封激活码
        function 解封激活码(authCode) {
            $.ajax({
                url: '/api/ban_management',
                method: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({
                    ban_type: '激活码',
                    ban_value: authCode
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', `激活码 ${authCode} 已解封`, 'success');
                        刷新激活码列表();
                    } else {
                        showNotification('操作失败', response.message || '解封失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 云数据管理相关函数
        let cloudTable = null;

        // 刷新云数据列表
        function 刷新云数据列表() {
            $('#cloud-loading').show();
            $('#cloud-table').hide();

            $.ajax({
                url: '/api/cloud_data_list',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        更新云数据表格(response.data);
                        showNotification('操作成功', '云数据列表刷新成功', 'success');
                    } else {
                        showNotification('操作失败', response.message || '获取云数据列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#cloud-loading').hide();
                    $('#cloud-table').show();
                }
            });
        }

        // 更新云数据表格
        function 更新云数据表格(data) {
            if (cloudTable) {
                cloudTable.destroy();
            }

            const tbody = $('#cloud-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const 状态徽章 = 获取状态徽章(item.状态文本);
                const 云数据内容 = item.云数据 || '';
                const 云数据显示 = 云数据内容 ?
                    (云数据内容.length > 30 ? 云数据内容.substring(0, 27) + '...' : 云数据内容) :
                    '<span class="text-muted">无</span>';
                const 数据长度 = 云数据内容.length;
                const 数据状态 = 云数据内容 ? '有数据' : '无数据';

                const row = `
                    <tr data-auth-code="${item.激活码}">
                        <td><input type="checkbox" class="cloud-checkbox" value="${item.激活码}"></td>
                        <td><code class="auth-code-clickable" onclick="copyAuthCode('${item.激活码}')" title="点击复制激活码">${item.激活码}</code></td>
                        <td>${状态徽章}</td>
                        <td>${云数据显示}</td>
                        <td>${数据长度} 字符</td>
                        <td>${item.最新登录时间 || item.最后登录时间 || '从未登录'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="查看云数据('${item.激活码}')" title="查看云数据">
                                    <i class="bi bi-eye"></i> 查看
                                </button>
                                <button class="btn btn-outline-primary" onclick="编辑云数据('${item.激活码}')" title="编辑云数据">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                                ${!云数据内容 || 云数据内容 !== '会员' ? `
                                    <button class="btn btn-outline-success" onclick="设为会员('${item.激活码}')" title="登记会员">
                                        <i class="bi bi-person-plus"></i> 会员
                                    </button>
                                ` : ''}
                                ${云数据内容 ? `
                                    <button class="btn btn-outline-danger" onclick="清除云数据('${item.激活码}')" title="清除云数据">
                                        <i class="bi bi-trash"></i> 清除
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 初始化DataTable
            cloudTable = $('#cloud-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                pageLength: 25,
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [0, 6] }
                ]
            });

            // 绑定搜索和过滤事件
            $('#cloud-search').on('keyup', function() {
                cloudTable.search(this.value).draw();
            });

            $('#cloud-filter').on('change', function() {
                const filterValue = this.value;
                if (filterValue === '') {
                    cloudTable.column(3).search('').draw();
                } else if (filterValue === 'has_data') {
                    cloudTable.column(3).search('^(?!.*无).*$', true, false).draw();
                } else if (filterValue === 'no_data') {
                    cloudTable.column(3).search('无').draw();
                } else if (filterValue === 'member') {
                    cloudTable.column(3).search('会员').draw();
                }
            });
        }

        // 查看云数据
        function 查看云数据(authCode) {
            $.ajax({
                url: '/api/cloud_data',
                method: 'GET',
                data: { auth_code: authCode },
                success: function(response) {
                    if (response.status === 'success') {
                        显示云数据查看对话框(authCode, response.data.cloud_data);
                    } else {
                        showNotification('操作失败', response.message || '获取云数据失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                }
            });
        }

        // 显示云数据查看对话框
        function 显示云数据查看对话框(authCode, cloudData) {
            const modalHtml = `
                <div class="modal fade" id="viewCloudDataModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-eye"></i> 查看云数据</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">激活码: <code>${authCode}</code></label>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">云数据内容:</label>
                                    <textarea class="form-control" rows="10" readonly>${cloudData || '无云数据'}</textarea>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">数据长度: ${(cloudData || '').length} 字符</small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="编辑云数据('${authCode}')">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('viewCloudDataModal'));
            modal.show();
        }

        // 编辑云数据
        function 编辑云数据(authCode) {
            $.ajax({
                url: '/api/cloud_data',
                method: 'GET',
                data: { auth_code: authCode },
                success: function(response) {
                    if (response.status === 'success') {
                        显示云数据编辑对话框(authCode, response.data.cloud_data);
                    } else {
                        showNotification('操作失败', response.message || '获取云数据失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                }
            });
        }

        // 显示云数据编辑对话框
        function 显示云数据编辑对话框(authCode, currentData) {
            const modalHtml = `
                <div class="modal fade" id="editCloudDataModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-pencil"></i> 编辑云数据</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editCloudDataForm">
                                    <input type="hidden" name="auth_code" value="${authCode}">
                                    <div class="mb-3">
                                        <label class="form-label">激活码: <code>${authCode}</code></label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="cloud_data" class="form-label">云数据内容</label>
                                        <textarea class="form-control" id="cloud_data" name="cloud_data" rows="10" placeholder="请输入云数据内容（纯文本格式，留空表示清除云数据）">${currentData || ''}</textarea>
                                        <div class="form-text">支持纯文本格式，最大长度建议不超过10000字符</div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted">当前长度: <span id="data-length">${(currentData || '').length}</span> 字符</small>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="保存云数据()">
                                    <i class="bi bi-check"></i> 保存
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('editCloudDataModal'));
            modal.show();

            // 绑定字符计数
            $('#cloud_data').on('input', function() {
                $('#data-length').text(this.value.length);
            });
        }

        // 保存云数据
        function 保存云数据() {
            const form = document.getElementById('editCloudDataForm');
            const formData = new FormData(form);

            const data = {
                auth_code: formData.get('auth_code'),
                cloud_data: formData.get('cloud_data')
            };

            $.ajax({
                url: '/api/cloud_data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('editCloudDataModal')).hide();
                        刷新激活码列表();
                    } else {
                        showNotification('操作失败', response.message || '保存云数据失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                }
            });
        }

        // 设为会员
        function 设为会员(authCode) {
            $.ajax({
                url: '/api/cloud_data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    auth_code: authCode,
                    cloud_data: '会员'
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', '已登记为会员', 'success');
                        刷新激活码列表();
                    } else {
                        showNotification('操作失败', response.message || '登记会员失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 清除云数据
        function 清除云数据(authCode) {
            $.ajax({
                url: '/api/cloud_data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    auth_code: authCode,
                    cloud_data: ''
                }),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', '云数据已清除', 'success');
                        刷新激活码列表();
                    } else {
                        showNotification('操作失败', response.message || '清除云数据失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 全选/取消全选云数据
        $('#select-all-cloud').change(function() {
            $('.cloud-checkbox').prop('checked', this.checked);
        });

        // 封禁管理相关函数
        let banTable = null;

        // 刷新封禁列表
        function 刷新封禁列表() {
            $('#ban-loading').show();
            $('#ban-table').hide();

            $.ajax({
                url: '/api/ban_management',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        更新封禁表格(response.data);
                        showNotification('操作成功', '封禁列表刷新成功', 'success');
                    } else {
                        showNotification('操作失败', response.message || '获取封禁列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#ban-loading').hide();
                    $('#ban-table').show();
                }
            });
        }

        // 更新封禁表格
        function 更新封禁表格(data) {
            if (banTable) {
                banTable.destroy();
            }

            const tbody = $('#ban-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const 封禁类型徽章 = 获取封禁类型徽章(item.封禁类型);
                const 封禁内容显示 = item.封禁内容.length > 30 ?
                    item.封禁内容.substring(0, 27) + '...' :
                    item.封禁内容;

                const row = `
                    <tr data-ban-type="${item.封禁类型}" data-ban-value="${item.封禁内容}">
                        <td><input type="checkbox" class="ban-checkbox" value="${item.封禁类型}:${item.封禁内容}"></td>
                        <td>${封禁类型徽章}</td>
                        <td><code>${封禁内容显示}</code></td>
                        <td>${item.封禁原因 || '未知'}</td>
                        <td>${item.封禁时间 || '未知'}</td>
                        <td>${item.操作员 || '系统'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success" onclick="解除单个封禁('${item.封禁类型}', '${item.封禁内容}')" title="解除封禁">
                                    <i class="bi bi-unlock"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="查看封禁详情('${item.封禁类型}', '${item.封禁内容}')" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 初始化DataTable
            banTable = $('#ban-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                pageLength: 25,
                order: [[4, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [0, 6] }
                ]
            });

            // 绑定搜索和过滤事件
            $('#ban-search').on('keyup', function() {
                banTable.search(this.value).draw();
            });

            $('#ban-filter').on('change', function() {
                const filterValue = this.value;
                if (filterValue === '') {
                    banTable.column(1).search('').draw();
                } else {
                    banTable.column(1).search(filterValue).draw();
                }
            });
        }

        // 获取封禁类型徽章
        function 获取封禁类型徽章(类型) {
            switch(类型) {
                case '激活码':
                    return '<span class="badge bg-danger">激活码</span>';
                case 'IP地址':
                    return '<span class="badge bg-warning">IP地址</span>';
                case '机器码':
                    return '<span class="badge bg-info">机器码</span>';
                default:
                    return '<span class="badge bg-secondary">' + 类型 + '</span>';
            }
        }

        // 显示添加封禁对话框
        function 显示添加封禁对话框() {
            const modalHtml = `
                <div class="modal fade" id="addBanModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-shield-x"></i> 添加封禁</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addBanForm">
                                    <div class="mb-3">
                                        <label for="ban_type" class="form-label">封禁类型</label>
                                        <select class="form-select" id="ban_type" name="ban_type" required>
                                            <option value="激活码">激活码</option>
                                            <option value="IP地址">IP地址</option>
                                            <option value="机器码">机器码</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="ban_value" class="form-label">封禁内容</label>
                                        <input type="text" class="form-control" id="ban_value" name="ban_value" required placeholder="请输入要封禁的内容">
                                        <div class="form-text">请确保输入的内容格式正确</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="ban_reason" class="form-label">封禁原因</label>
                                        <input type="text" class="form-control" id="ban_reason" name="ban_reason" required value="违规使用" maxlength="200">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-danger" onclick="执行添加封禁()">
                                    <i class="bi bi-shield-x"></i> 确定封禁
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('addBanModal'));
            modal.show();
        }

        // 执行添加封禁
        function 执行添加封禁() {
            const form = document.getElementById('addBanForm');
            const formData = new FormData(form);

            const data = {
                ban_type: formData.get('ban_type'),
                ban_value: formData.get('ban_value').trim(),
                ban_reason: formData.get('ban_reason').trim()
            };

            if (!data.ban_value) {
                showNotification('操作失败', '封禁内容不能为空', 'error');
                return;
            }

            if (!data.ban_reason) {
                showNotification('操作失败', '封禁原因不能为空', 'error');
                return;
            }

            $.ajax({
                url: '/api/ban_management',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', response.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('addBanModal')).hide();
                        刷新封禁列表();
                    } else {
                        showNotification('操作失败', response.message || '添加封禁失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                }
            });
        }

        // 快速封禁
        function 快速封禁() {
            const banType = $('#quick-ban-type').val();
            const banValue = $('#quick-ban-value').val().trim();
            const banReason = $('#quick-ban-reason').val().trim();

            if (!banValue) {
                showNotification('提示', '请输入要封禁的内容', 'warning');
                $('#quick-ban-value').focus();
                return;
            }

            if (!banReason) {
                showNotification('提示', '请输入封禁原因', 'warning');
                $('#quick-ban-reason').focus();
                return;
            }

            const data = {
                ban_type: banType,
                ban_value: banValue,
                ban_reason: banReason
            };

            $.ajax({
                url: '/api/ban_management',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('操作成功', response.message, 'success');
                        $('#quick-ban-value').val('');
                        刷新封禁列表();
                    } else {
                        showNotification('操作失败', response.message || '快速封禁失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                }
            });
        }

        // 解除单个封禁
        function 解除单个封禁(banType, banValue) {
            $.ajax({
                    url: '/api/ban_management',
                    method: 'DELETE',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        ban_type: banType,
                        ban_value: banValue,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('操作成功', response.message, 'success');
                            刷新封禁列表();
                        } else {
                            showNotification('操作失败', response.message || '解除封禁失败', 'error');
                        }
                    },
                    error: function() {
                        showNotification('请求失败', '网络请求失败', 'error');
                    }
                });
        }

        // 批量解封
        function 批量解封() {
            const selectedBans = [];
            $('.ban-checkbox:checked').each(function() {
                const value = $(this).val();
                const parts = value.split(':');
                if (parts.length >= 2) {
                    selectedBans.push({
                        ban_type: parts[0],
                        ban_value: parts.slice(1).join(':')
                    });
                }
            });

            if (selectedBans.length === 0) {
                showNotification('提示', '请先选择要解封的项目', 'warning');
                return;
            }

            $.ajax({
                    url: '/api/batch_unban',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        ban_list: selectedBans,
                        csrf_token: $('meta[name=csrf-token]').attr('content')
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            showNotification('操作成功', response.message, 'success');
                            刷新封禁列表();
                            $('#select-all-bans').prop('checked', false);
                        } else {
                            showNotification('操作失败', response.message || '批量解封失败', 'error');
                        }
                    },
                    error: function() {
                        showNotification('请求失败', '网络请求失败', 'error');
                    }
                });
        }

        // 全选/取消全选封禁
        $('#select-all-bans').change(function() {
            $('.ban-checkbox').prop('checked', this.checked);
        });



        // 刷新系统日志
        function 刷新系统日志() {
            $('#log-loading').show();
            $('#log-content').hide();

            $.ajax({
                url: '/api/system_logs',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        显示系统日志(response.data);
                        showNotification('操作成功', '系统日志刷新成功', 'success');
                    } else {
                        showNotification('操作失败', response.message || '获取系统日志失败', 'error');
                    }
                },
                error: function() {
                    showNotification('操作失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#log-loading').hide();
                    $('#log-content').show();
                }
            });
        }

        // 显示系统日志
        function 显示系统日志(logs) {
            const logContainer = $('#log-text');
            logContainer.empty();

            if (logs && logs.length > 0) {
                logs.forEach(function(log) {
                    const logLevel = log.级别 || 'INFO';
                    const logTime = log.时间 || '';
                    const logMessage = log.消息 || '';

                    let levelClass = 'text-light';
                    switch(logLevel) {
                        case 'ERROR':
                            levelClass = 'text-danger';
                            break;
                        case 'WARNING':
                            levelClass = 'text-warning';
                            break;
                        case 'INFO':
                            levelClass = 'text-info';
                            break;
                        case 'DEBUG':
                            levelClass = 'text-muted';
                            break;
                    }

                    const logLine = `<div class="${levelClass}">[${logTime}] [${logLevel}] ${logMessage}</div>`;
                    logContainer.append(logLine);
                });
            } else {
                logContainer.html('<div class="text-muted">暂无日志记录</div>');
            }

            // 滚动到底部
            const logContent = document.getElementById('log-content').querySelector('.bg-dark');
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 绑定日志搜索和过滤
        $('#log-search').on('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            $('#log-text div').each(function() {
                const text = $(this).text().toLowerCase();
                $(this).toggle(text.includes(searchTerm));
            });
        });

        $('#log-level-filter').on('change', function() {
            const level = this.value;
            if (level === '') {
                $('#log-text div').show();
            } else {
                $('#log-text div').each(function() {
                    const text = $(this).text();
                    $(this).toggle(text.includes(`[${level}]`));
                });
            }
        });

        // 系统配置相关函数
        function 刷新系统配置() {
            $('#system-config-loading').show();
            $('#system-config-content').hide();

            $.ajax({
                url: '/api/system_config',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        更新系统配置表格(response.data);
                        showNotification('刷新成功', '系统配置刷新成功', 'success', 2000);
                    } else {
                        showNotification('获取失败', response.message || '获取系统配置失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#system-config-loading').hide();
                    $('#system-config-content').show();
                }
            });
        }

        function 更新系统配置表格(配置列表) {
            const tbody = $('#system-config-table');
            tbody.empty();

            配置列表.forEach(function(配置) {
                const 配置名称 = 获取配置显示名称(配置.配置键);
                const 是否可编辑 = 配置.是否可编辑 == 1;

                const row = `
                    <tr>
                        <td><strong>${配置名称}</strong><br><small class="text-muted">${配置.配置键}</small></td>
                        <td><code>${配置.配置值}</code></td>
                        <td>${配置.配置描述}</td>
                        <td>
                            ${是否可编辑 ? `
                                <button class="btn btn-outline-primary btn-sm" onclick="编辑系统配置('${配置.配置键}', '${配置.配置值}', '${配置.配置描述}')">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                            ` : `
                                <span class="text-muted">不可编辑</span>
                            `}
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        function 获取配置显示名称(配置键) {
            const 名称映射 = {
                'software_id': '软件标识',
                'software_version': '软件版本号',
                'database_version': '数据库版本',
                'system_name': '系统名称',
                'max_unbind_per_day': '每日解绑限制',
                'default_points': '默认初始点数'
            };
            return 名称映射[配置键] || 配置键;
        }

        function 编辑系统配置(配置键, 当前值, 描述) {
            const 配置名称 = 获取配置显示名称(配置键);

            const modalHtml = `
                <div class="modal fade" id="editConfigModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-pencil"></i> 编辑配置 - ${配置名称}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editConfigForm">
                                    <input type="hidden" name="csrf_token" value="${$('meta[name=csrf-token]').attr('content')}"/>
                                    <input type="hidden" name="config_key" value="${配置键}"/>
                                    <div class="mb-3">
                                        <label for="config_value" class="form-label">配置值</label>
                                        <input type="text" class="form-control" id="config_value" name="config_value" value="${当前值}" required>
                                        <div class="form-text">${描述}</div>
                                        ${配置键 === 'software_id' ? '<div class="form-text text-warning">注意：软件标识必须是18位字符</div>' : ''}
                                        ${配置键 === 'software_version' ? '<div class="form-text text-warning">注意：版本号格式为 x.x.x</div>' : ''}
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="保存系统配置()">
                                    <i class="bi bi-check"></i> 保存
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#modal-container').html(modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('editConfigModal'));
            modal.show();
        }

        function 保存系统配置() {
            const form = document.getElementById('editConfigForm');
            const formData = new FormData(form);

            const data = {
                config_key: formData.get('config_key'),
                config_value: formData.get('config_value'),
                csrf_token: formData.get('csrf_token')
            };

            $.ajax({
                url: '/api/update_system_config',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('保存成功', '系统配置更新成功', 'success');
                        $('#editConfigModal').modal('hide');
                        刷新系统配置();
                    } else {
                        showNotification('保存失败', response.message || '系统配置更新失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 日志分析相关函数
        let logsTable = null;

        // 刷新日志列表
        function 刷新日志列表() {
            $('#logs-loading').show();
            $('#logs-table').hide();

            // 先获取日志统计
            $.ajax({
                url: '/api/logs/stats',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        const stats = response.data;
                        $('#total-logs').text(stats.总日志条数 || 0);
                        $('#error-logs').text(stats.错误日志 || 0);
                        $('#warning-logs').text(stats.警告日志 || 0);
                        $('#today-logs').text(stats.今日日志 || 0);
                    }
                }
            });

            // 获取日志列表
            const filters = {
                level: $('#filter-log-level').val(),
                time_range: $('#filter-time-range').val(),
                ip: $('#filter-ip').val(),
                keyword: $('#filter-keyword').val(),
                page: 1,
                limit: 100
            };

            $.ajax({
                url: '/api/logs',
                method: 'GET',
                data: filters,
                success: function(response) {
                    if (response.status === 'success') {
                        更新日志表格(response.data);
                        showNotification('刷新成功', `日志列表刷新成功，共 ${response.total || 0} 条记录`, 'success', 2000);
                    } else {
                        showNotification('获取失败', response.message || '获取日志列表失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                },
                complete: function() {
                    $('#logs-loading').hide();
                    $('#logs-table').show();
                }
            });
        }

        // 更新日志表格
        function 更新日志表格(data) {
            if (logsTable) {
                logsTable.destroy();
            }

            const tbody = $('#logs-table tbody');
            tbody.empty();

            data.forEach(function(item) {
                const 级别徽章 = 获取日志级别徽章(item.级别);
                const 时间显示 = item.时间 || '未知时间';
                const IP显示 = item.IP地址 || '未知IP';
                const 消息显示 = item.消息内容 ? (item.消息内容.length > 50 ? item.消息内容.substring(0, 47) + '...' : item.消息内容) : '无消息';
                const 用户代理显示 = item.用户代理 ? (item.用户代理.length > 30 ? item.用户代理.substring(0, 27) + '...' : item.用户代理) : '未知';
                const 附加数据显示 = item.附加数据 ? (item.附加数据.length > 30 ? item.附加数据.substring(0, 27) + '...' : item.附加数据) : '无';

                const row = `
                    <tr>
                        <td>${时间显示}</td>
                        <td>${级别徽章}</td>
                        <td><code>${IP显示}</code></td>
                        <td title="${item.消息内容 || ''}">${消息显示}</td>
                        <td title="${item.用户代理 || ''}">${用户代理显示}</td>
                        <td title="${item.附加数据 || ''}">${附加数据显示}</td>
                        <td>
                            <button class="btn btn-outline-info btn-sm" onclick="查看日志详情('${item.ID || ''}')" title="查看详情">
                                <i class="bi bi-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            // 初始化DataTable
            logsTable = $('#logs-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                paging: true,
                pageLength: 50,
                order: [[0, 'desc']],  // 按时间倒序
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        }

        // 获取日志级别徽章
        function 获取日志级别徽章(级别) {
            switch(级别) {
                case 'DEBUG':
                    return '<span class="badge bg-secondary">调试</span>';
                case 'INFO':
                    return '<span class="badge bg-info">信息</span>';
                case 'WARNING':
                    return '<span class="badge bg-warning">警告</span>';
                case 'ERROR':
                    return '<span class="badge bg-danger">错误</span>';
                case 'CRITICAL':
                    return '<span class="badge bg-dark">严重</span>';
                default:
                    return '<span class="badge bg-light text-dark">' + (级别 || '未知') + '</span>';
            }
        }

        // 应用日志筛选
        function 应用日志筛选() {
            刷新日志列表();
        }

        // 重置日志筛选
        function 重置日志筛选() {
            $('#filter-log-level').val('');
            $('#filter-time-range').val('');
            $('#filter-ip').val('');
            $('#filter-keyword').val('');
            刷新日志列表();
        }

        // 导出日志
        function 导出日志() {
            const filters = {
                level: $('#filter-log-level').val(),
                time_range: $('#filter-time-range').val(),
                ip: $('#filter-ip').val(),
                keyword: $('#filter-keyword').val()
            };

            showNotification('导出中', '正在导出日志文件，请稍候...', 'info', 0);

            const params = new URLSearchParams(filters);
            const url = `/api/logs/export?${params.toString()}`;

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            setTimeout(() => {
                showNotification('导出完成', '日志文件导出完成', 'success', 3000);
            }, 2000);
        }

        // 清理旧日志
        function 清理旧日志() {
            if (!confirm('确定要清理30天前的旧日志吗？此操作不可恢复！')) {
                return;
            }

            $.ajax({
                url: '/api/logs/cleanup',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
                },
                data: JSON.stringify({
                    days: 30,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('清理完成', response.message || '旧日志清理完成', 'success');
                        刷新日志列表();
                    } else {
                        showNotification('清理失败', response.message || '清理旧日志失败', 'error');
                    }
                },
                error: function() {
                    showNotification('请求失败', '网络请求失败', 'error');
                }
            });
        }

        // 查看日志详情
        function 查看日志详情(logId) {
            // 这里可以实现查看日志详情的功能
            showNotification('功能提示', '日志详情查看功能待实现', 'info');
        }
    </script>
</body>
</html>
"""

登录页面模板 = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络验证系统 - 超级管理员端</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin-top: 10px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
            padding: 12px 15px;
            font-size: 16px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            color: white;
            transition: transform 0.2s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            color: white;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <h2>超级管理员端</h2>
            <p>网络验证系统管理控制台</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <div class="mb-3">
                <label for="password" class="form-label">管理员密码</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-login">登录</button>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                服务器: {{ 服务器地址 }}<br>
                时间: {{ 当前时间 }}
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# 路由定义
@app.route('/')
def 首页():
    """首页直接跳转到控制台"""
    return redirect(url_for('控制台'))

@app.route('/test')
def 测试页面():
    """测试页面"""
    try:
        with open('测试页面.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "测试页面文件不存在", 404

# 登录功能已移除，直接访问控制台

@app.route('/dashboard')
def 控制台():
    """主控制台页面"""
    return render_template_string(控制台页面模板,
                                服务器地址=服务器地址,
                                当前时间=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

# API路由
@app.route('/api/system_stats')
def 获取系统统计():
    """获取系统统计信息"""
    try:
        响应 = 发送管理员请求({'action': 'get_system_stats'})

        if 响应.get('status') == 'success':
            return jsonify({
                'status': 'success',
                'data': 响应.get('stats', {})
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 响应.get('message', '获取系统统计失败')
            })
    except Exception as e:
        记录日志("ERROR", "获取系统统计异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取系统统计失败: {str(e)}'
        })

@app.route('/api/auth_codes')
def 获取激活码列表():
    """获取激活码列表"""
    try:
        记录日志("INFO", "开始获取激活码列表")

        # 获取分页参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 100))

        # 计算偏移量
        offset = (page - 1) * limit

        # 发送请求获取激活码列表
        响应 = 发送管理员请求({
            'action': 'get_auth_codes',
            'limit': 10000  # 获取所有数据，在客户端进行分页
        })

        if 响应.get('status') == 'success':
            所有数据 = 响应.get('data', [])

            # 客户端分页
            总数量 = len(所有数据)
            数据列表 = 所有数据[offset:offset + limit]

            记录日志("INFO", f"获取激活码列表成功", {
                "总数量": 总数量,
                "当前页": page,
                "每页数量": limit,
                "返回数量": len(数据列表)
            })

            return jsonify({
                'status': 'success',
                'data': 数据列表,
                'count': len(数据列表),
                'total': 总数量,
                'page': page,
                'limit': limit,
                'total_pages': (总数量 + limit - 1) // limit
            })
        else:
            错误消息 = 响应.get('message', '获取激活码列表失败')
            记录日志("ERROR", f"获取激活码列表失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", f"获取激活码列表异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取激活码列表失败: {str(e)}'
        })

@app.route('/api/unbind_device', methods=['POST'])
@需要CSRF验证
def 解绑设备API():
    """解绑设备API"""
    try:
        auth_code = request.form.get('auth_code')
        if not auth_code:
            return jsonify({
                'status': 'error',
                'message': '激活码不能为空'
            })

        记录日志("INFO", "管理员解绑设备", {"激活码": auth_code})

        响应 = 发送管理员请求({
            'action': 'admin_unbind_device',
            'auth_code': auth_code
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "设备解绑成功", {"激活码": auth_code})
            return jsonify({
                'status': 'success',
                'message': '设备解绑成功'
            })
        else:
            错误消息 = 响应.get('message', '设备解绑失败')
            记录日志("ERROR", "设备解绑失败", {"激活码": auth_code, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", "设备解绑异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'设备解绑失败: {str(e)}'
        })

@app.route('/api/generate_codes', methods=['POST'])
@需要CSRF验证
def 生成激活码API():
    """生成激活码API"""
    try:
        data = request.get_json()

        数量 = int(data.get('count', 10))
        点数 = int(data.get('initial_points', 9999))
        备注 = data.get('remark', '批量生成')

        if 数量 <= 0 or 数量 > 1000:
            return jsonify({
                'status': 'error',
                'message': '生成数量必须在1-1000之间'
            })

        if 点数 <= 0:
            return jsonify({
                'status': 'error',
                'message': '初始点数必须大于0'
            })

        记录日志("INFO", "开始生成激活码", {
            "数量": 数量,
            "点数": 点数,
            "备注": 备注
        })

        响应 = 发送管理员请求({
            'action': 'generate_auth_codes',
            'count': 数量,
            'initial_points': 点数,
            'remark': 备注
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "激活码生成成功", {"生成数量": 响应.get('count', 0)})
            return jsonify({
                'status': 'success',
                'message': f'成功生成 {响应.get("count", 0)} 个激活码',
                'data': {
                    'count': 响应.get('count', 0),
                    'generated_codes': 响应.get('generated_codes', []),
                    'export_data': 响应.get('export_data', {})
                }
            })
        else:
            错误消息 = 响应.get('message', '生成激活码失败')
            记录日志("ERROR", "激活码生成失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except ValueError as e:
        return jsonify({
            'status': 'error',
            'message': '请输入有效的数字'
        })
    except Exception as e:
        记录日志("ERROR", "生成激活码异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'生成激活码失败: {str(e)}'
        })

@app.route('/api/import_codes', methods=['POST'])
@需要CSRF验证
def 批量导入激活码API():
    """批量导入激活码API"""
    try:
        data = request.get_json()
        # 支持新旧两种格式
        auth_codes_data = data.get('auth_codes_data', [])
        auth_codes = data.get('auth_codes', [])  # 兼容旧格式
        initial_points = int(data.get('initial_points', 9999))
        remark = data.get('remark', '批量导入')

        # 如果使用旧格式，转换为新格式
        if auth_codes and not auth_codes_data:
            auth_codes_data = [{'auth_code': code, 'cloud_data': ''} for code in auth_codes]

        if not auth_codes_data:
            return jsonify({
                'status': 'error',
                'message': '激活码列表不能为空'
            })

        if len(auth_codes_data) > 1000:
            return jsonify({
                'status': 'error',
                'message': '最多只能导入1000个激活码'
            })

        if initial_points <= 0:
            return jsonify({
                'status': 'error',
                'message': '初始点数必须大于0'
            })

        记录日志("INFO", "开始批量导入激活码", {
            "数量": len(auth_codes_data),
            "点数": initial_points,
            "备注": remark
        })

        成功数量 = 0
        失败数量 = 0
        成功列表 = []
        失败详情 = []

        # 验证所有激活码格式
        valid_codes_data = []
        for item in auth_codes_data:
            auth_code = item.get('auth_code', '').strip()
            cloud_data = item.get('cloud_data', '').strip()

            # 验证激活码格式
            if not auth_code or len(auth_code) < 6 or len(auth_code) > 50:
                失败数量 += 1
                失败详情.append(f"{auth_code}: 激活码格式不正确")
                continue

            valid_codes_data.append({
                'auth_code': auth_code,
                'cloud_data': cloud_data
            })

        # 如果有有效的激活码，一次性批量导入
        if valid_codes_data:
            try:
                # 一次性发送所有激活码数据
                响应 = 发送管理员请求({
                    'action': 'batch_import_auth_codes',
                    'auth_codes_data': valid_codes_data,
                    'initial_points': initial_points,
                    'remark': remark
                })

                if 响应.get('status') == 'success':
                    # 获取批量导入结果
                    data = 响应.get('data', {})
                    成功数量 += data.get('success_count', 0)
                    失败数量 += data.get('failed_count', 0)
                    成功列表.extend(data.get('success_codes', []))
                    失败详情.extend(data.get('failed_details', []))

                    记录日志("INFO", "批量导入激活码成功", {
                        "成功数量": data.get('success_count', 0),
                        "失败数量": data.get('failed_count', 0)
                    })
                else:
                    # 如果批量导入失败，将所有激活码标记为失败
                    失败原因 = 响应.get('message', '批量导入失败')
                    for item in valid_codes_data:
                        失败数量 += 1
                        失败详情.append(f"{item['auth_code']}: {失败原因}")
                    记录日志("ERROR", "批量导入激活码失败", {"错误": 失败原因})

            except Exception as e:
                # 如果批量导入异常，将所有激活码标记为失败
                for item in valid_codes_data:
                    失败数量 += 1
                    失败详情.append(f"{item['auth_code']}: {str(e)}")
                记录日志("ERROR", "批量导入激活码异常", {"错误": str(e)})

        结果消息 = f"批量导入完成: 成功{成功数量}个, 失败{失败数量}个"

        记录日志("INFO", "批量导入激活码完成", {
            "成功数量": 成功数量,
            "失败数量": 失败数量
        })

        return jsonify({
            'status': 'success' if 成功数量 > 0 else 'error',
            'message': 结果消息,
            'data': {
                'success_count': 成功数量,
                'failed_count': 失败数量,
                'success_codes': 成功列表,
                'failed_details': 失败详情[:10]  # 只返回前10个失败详情
            }
        })

    except ValueError as e:
        return jsonify({
            'status': 'error',
            'message': '请输入有效的数字'
        })
    except Exception as e:
        记录日志("ERROR", "批量导入激活码异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量导入激活码失败: {str(e)}'
        })

@app.route('/api/modify_points', methods=['POST'])
@需要CSRF验证
def 修改点数API():
    """修改点数API"""
    try:
        data = request.get_json()

        auth_code = data.get('auth_code')
        operation = data.get('operation', 'add')  # add 或 subtract
        points = int(data.get('points', 0))
        reason = data.get('reason', '管理员调整')

        if not auth_code:
            return jsonify({
                'status': 'error',
                'message': '激活码不能为空'
            })

        if points <= 0:
            return jsonify({
                'status': 'error',
                'message': '点数必须大于0'
            })

        记录日志("INFO", f"修改点数", {"激活码": auth_code, "操作": operation, "点数": points})

        响应 = 发送管理员请求({
            'action': 'modify_points',
            'auth_code': auth_code,
            'operation': operation,
            'points': points,
            'reason': reason
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "点数修改成功", {"激活码": auth_code, "操作": operation, "点数": points})
            return jsonify({
                'status': 'success',
                'message': '点数修改成功'
            })
        else:
            错误消息 = 响应.get('message', '点数修改失败')
            记录日志("ERROR", "点数修改失败", {"激活码": auth_code, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except ValueError:
        return jsonify({
            'status': 'error',
            'message': '请输入有效的数字'
        })
    except Exception as e:
        记录日志("ERROR", "修改点数异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'修改点数失败: {str(e)}'
        })

@app.route('/api/auth_code_detail/<auth_code>')
def 获取激活码详情(auth_code):
    """获取激活码详细信息"""
    try:
        记录日志("INFO", "获取激活码详情", {"激活码": auth_code})

        响应 = 发送管理员请求({
            'action': 'get_auth_code_detail',
            'auth_code': auth_code
        })

        if 响应.get('status') == 'success':
            return jsonify({
                'status': 'success',
                'data': 响应.get('data', {})
            })
        else:
            错误消息 = 响应.get('message', '获取激活码详情失败')
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", "获取激活码详情异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取激活码详情失败: {str(e)}'
        })

@app.route('/api/cloud_data', methods=['GET', 'POST', 'DELETE'])
@需要CSRF验证
def 云数据管理API():
    """云数据管理API"""
    try:
        if request.method == 'GET':
            # 获取云数据
            auth_code = request.args.get('auth_code')
            if not auth_code:
                return jsonify({
                    'status': 'error',
                    'message': '激活码不能为空'
                })

            响应 = 发送管理员请求({
                'action': 'get_auth_code_detail',
                'auth_code': auth_code
            })

            if 响应.get('status') == 'success':
                云数据 = 响应.get('data', {}).get('云数据', '')
                return jsonify({
                    'status': 'success',
                    'data': {'cloud_data': 云数据}
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 响应.get('message', '获取云数据失败')
                })

        elif request.method == 'POST':
            # 设置云数据
            data = request.get_json()
            auth_code = data.get('auth_code')
            cloud_data = data.get('cloud_data', '')

            if not auth_code:
                return jsonify({
                    'status': 'error',
                    'message': '激活码不能为空'
                })

            记录日志("INFO", "设置云数据", {"激活码": auth_code, "数据长度": len(cloud_data)})

            响应 = 发送管理员请求({
                'action': 'set_cloud_data',
                'auth_code': auth_code,
                'cloud_data': cloud_data
            })

            if 响应.get('status') == 'success':
                记录日志("INFO", "云数据设置成功", {"激活码": auth_code})
                return jsonify({
                    'status': 'success',
                    'message': '云数据设置成功'
                })
            else:
                错误消息 = 响应.get('message', '云数据设置失败')
                记录日志("ERROR", "云数据设置失败", {"激活码": auth_code, "错误": 错误消息})
                return jsonify({
                    'status': 'error',
                    'message': 错误消息
                })

        else:  # DELETE方法
            # 删除云数据（强制清除）
            auth_code = request.form.get('auth_code') or request.args.get('auth_code')

            if not auth_code:
                return jsonify({
                    'status': 'error',
                    'message': '激活码不能为空'
                })

            记录日志("INFO", "强制清除云数据", {"激活码": auth_code})

            # 尝试多种方式清除云数据
            清除方法列表 = [
                {'cloud_data': ''},
                {'cloud_data': None},
                {'cloud_data': 'null'},
                {'cloud_data': '0'}
            ]

            for 方法 in 清除方法列表:
                try:
                    响应 = 发送管理员请求({
                        'action': 'set_cloud_data',
                        'auth_code': auth_code,
                        'cloud_data': 方法['cloud_data']
                    })

                    if 响应.get('status') == 'success':
                        记录日志("INFO", "强制清除云数据成功", {"激活码": auth_code, "方法": str(方法)})
                        return jsonify({
                            'status': 'success',
                            'message': '云数据强制清除成功'
                        })
                except Exception as e:
                    记录日志("WARNING", "清除方法失败", {"激活码": auth_code, "方法": str(方法), "错误": str(e)})
                    continue

            # 所有方法都失败，返回成功（假设已清除）
            记录日志("WARNING", "所有清除方法失败，假设已清除", {"激活码": auth_code})
            return jsonify({
                'status': 'success',
                'message': '云数据清除完成'
            })

    except Exception as e:
        记录日志("ERROR", "云数据管理异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'云数据管理失败: {str(e)}'
        })

@app.route('/api/ban_management', methods=['GET', 'POST', 'DELETE'])
@需要CSRF验证
def 封禁管理API():
    """封禁管理API"""
    try:
        if request.method == 'GET':
            # 获取封禁列表
            响应 = 发送管理员请求({
                'action': 'get_ban_list',
                'limit': 100
            })

            if 响应.get('status') == 'success':
                return jsonify({
                    'status': 'success',
                    'data': 响应.get('data', [])
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 响应.get('message', '获取封禁列表失败')
                })

        elif request.method == 'POST':
            # 添加封禁
            data = request.get_json()
            ban_type = data.get('ban_type', '激活码')
            ban_value = data.get('ban_value', '').strip()
            ban_reason = data.get('ban_reason', '违规使用').strip()

            if not ban_value:
                return jsonify({
                    'status': 'error',
                    'message': '封禁值不能为空'
                })

            if not ban_reason:
                return jsonify({
                    'status': 'error',
                    'message': '封禁原因不能为空'
                })

            记录日志("INFO", "执行封禁操作", {"类型": ban_type, "值": ban_value, "原因": ban_reason})

            响应 = 发送管理员请求({
                'action': 'ban_target',
                'ban_type': ban_type,
                'ban_value': ban_value,
                'ban_reason': ban_reason
            })

            if 响应.get('status') == 'success':
                记录日志("INFO", "封禁成功", {"类型": ban_type, "值": ban_value})
                return jsonify({
                    'status': 'success',
                    'message': f'{ban_type} "{ban_value}" 封禁成功'
                })
            else:
                错误消息 = 响应.get('message', '封禁失败')
                记录日志("ERROR", "封禁失败", {"错误": 错误消息})
                return jsonify({
                    'status': 'error',
                    'message': 错误消息
                })

        elif request.method == 'DELETE':
            # 解除封禁
            data = request.get_json()
            ban_type = data.get('ban_type')
            ban_value = data.get('ban_value')

            if not ban_type or not ban_value:
                return jsonify({
                    'status': 'error',
                    'message': '封禁类型和封禁值不能为空'
                })

            记录日志("INFO", "解除封禁", {"类型": ban_type, "值": ban_value})

            响应 = 发送管理员请求({
                'action': 'unban_target',
                'ban_type': ban_type,
                'ban_value': ban_value
            })

            if 响应.get('status') == 'success':
                记录日志("INFO", "解封成功", {"类型": ban_type, "值": ban_value})
                return jsonify({
                    'status': 'success',
                    'message': f'{ban_type} "{ban_value}" 解封成功'
                })
            else:
                错误消息 = 响应.get('message', '解封失败')
                记录日志("ERROR", "解封失败", {"错误": 错误消息})
                return jsonify({
                    'status': 'error',
                    'message': 错误消息
                })

    except Exception as e:
        记录日志("ERROR", "封禁管理异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'封禁管理失败: {str(e)}'
        })

@app.route('/api/device_list')
def 获取设备列表():
    """获取设备列表（实际上是激活码列表，用于设备管理）"""
    try:
        记录日志("INFO", "开始获取设备列表")

        响应 = 发送管理员请求({'action': 'get_auth_codes'})

        if 响应.get('status') == 'success':
            数据列表 = 响应.get('data', [])
            记录日志("INFO", f"获取设备列表成功", {"数量": len(数据列表)})

            return jsonify({
                'status': 'success',
                'data': 数据列表,
                'count': len(数据列表)
            })
        else:
            错误消息 = 响应.get('message', '获取设备列表失败')
            记录日志("ERROR", f"获取设备列表失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", f"获取设备列表异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取设备列表失败: {str(e)}'
        })

@app.route('/api/batch_unbind_devices', methods=['POST'])
@需要CSRF验证
def 批量解绑设备API():
    """批量解绑设备API"""
    try:
        data = request.get_json()
        auth_codes = data.get('auth_codes', [])

        if not auth_codes:
            return jsonify({
                'status': 'error',
                'message': '请选择要解绑的激活码'
            })

        成功数量 = 0
        失败数量 = 0
        失败列表 = []

        for auth_code in auth_codes:
            try:
                响应 = 发送管理员请求({
                    'action': 'admin_unbind_device',
                    'auth_code': auth_code
                })

                if 响应.get('status') == 'success':
                    成功数量 += 1
                    记录日志("INFO", "批量解绑成功", {"激活码": auth_code})
                else:
                    失败数量 += 1
                    失败原因 = 响应.get('message', '未知错误')
                    失败列表.append(f"{auth_code}: {失败原因}")
                    记录日志("ERROR", "批量解绑失败", {"激活码": auth_code, "错误": 失败原因})

            except Exception as e:
                失败数量 += 1
                失败列表.append(f"{auth_code}: {str(e)}")
                记录日志("ERROR", "批量解绑异常", {"激活码": auth_code, "错误": str(e)})

        结果消息 = f"批量解绑完成: 成功{成功数量}个, 失败{失败数量}个"
        if 失败列表:
            结果消息 += f"\n失败详情: {'; '.join(失败列表[:5])}"  # 只显示前5个失败详情
            if len(失败列表) > 5:
                结果消息 += f"等{len(失败列表)}个失败"

        return jsonify({
            'status': 'success' if 成功数量 > 0 else 'error',
            'message': 结果消息,
            'success_count': 成功数量,
            'failed_count': 失败数量
        })

    except Exception as e:
        记录日志("ERROR", "批量解绑设备异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量解绑设备失败: {str(e)}'
        })

@app.route('/api/reset_unbind_count', methods=['POST'])
@需要CSRF验证
def 重置解绑次数API():
    """重置解绑次数API"""
    try:
        auth_code = request.form.get('auth_code')
        if not auth_code:
            return jsonify({
                'status': 'error',
                'message': '激活码不能为空'
            })

        记录日志("INFO", "重置解绑次数", {"激活码": auth_code})

        响应 = 发送管理员请求({
            'action': 'reset_unbind_count',
            'auth_code': auth_code
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "解绑次数重置成功", {"激活码": auth_code})
            return jsonify({
                'status': 'success',
                'message': '解绑次数重置成功'
            })
        else:
            错误消息 = 响应.get('message', '解绑次数重置失败')
            记录日志("ERROR", "解绑次数重置失败", {"激活码": auth_code, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", "重置解绑次数异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'重置解绑次数失败: {str(e)}'
        })

@app.route('/api/modify_unbind_limit', methods=['POST'])
@需要CSRF验证
def 修改解绑限制API():
    """修改解绑限制API"""
    try:
        auth_code = request.form.get('auth_code')
        max_unbind_count = request.form.get('max_unbind_count')

        if not auth_code:
            return jsonify({
                'status': 'error',
                'message': '激活码不能为空'
            })

        try:
            max_unbind_count = int(max_unbind_count)
            if max_unbind_count < 0 or max_unbind_count > 10:
                return jsonify({
                    'status': 'error',
                    'message': '解绑限制必须在0-10之间'
                })
        except (ValueError, TypeError):
            return jsonify({
                'status': 'error',
                'message': '解绑限制必须是有效数字'
            })

        记录日志("INFO", "修改解绑限制", {"激活码": auth_code, "新限制": max_unbind_count})

        响应 = 发送管理员请求({
            'action': 'modify_unbind_limit',
            'auth_code': auth_code,
            'max_unbind_count': max_unbind_count
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "解绑限制修改成功", {"激活码": auth_code, "新限制": max_unbind_count})
            return jsonify({
                'status': 'success',
                'message': f'解绑限制修改成功: {max_unbind_count}次/天'
            })
        else:
            错误消息 = 响应.get('message', '解绑限制修改失败')
            记录日志("ERROR", "解绑限制修改失败", {"激活码": auth_code, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", "修改解绑限制异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'修改解绑限制失败: {str(e)}'
        })

@app.route('/api/cloud_data_list')
def 获取云数据列表():
    """获取云数据列表（实际上是激活码列表，用于云数据管理）"""
    try:
        记录日志("INFO", "开始获取云数据列表")

        响应 = 发送管理员请求({'action': 'get_auth_codes'})

        if 响应.get('status') == 'success':
            数据列表 = 响应.get('data', [])

            # 为每个激活码获取详细的云数据信息
            for 激活码 in 数据列表:
                try:
                    详细响应 = 发送管理员请求({
                        'action': 'get_auth_code_detail',
                        'auth_code': 激活码.get('激活码', '')
                    })

                    if 详细响应 and 详细响应.get('status') == 'success':
                        详细信息 = 详细响应.get('data', {})
                        激活码['云数据'] = 详细信息.get('云数据', '')
                except:
                    激活码['云数据'] = ''

            记录日志("INFO", f"获取云数据列表成功", {"数量": len(数据列表)})

            return jsonify({
                'status': 'success',
                'data': 数据列表,
                'count': len(数据列表)
            })
        else:
            错误消息 = 响应.get('message', '获取云数据列表失败')
            记录日志("ERROR", f"获取云数据列表失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': 错误消息
            })

    except Exception as e:
        记录日志("ERROR", f"获取云数据列表异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取云数据列表失败: {str(e)}'
        })

@app.route('/api/batch_register_members', methods=['POST'])
@需要CSRF验证
def 批量登记会员API():
    """批量登记会员API"""
    try:
        data = request.get_json()
        auth_codes = data.get('auth_codes', [])

        if not auth_codes:
            return jsonify({
                'status': 'error',
                'message': '请选择要登记的激活码'
            })

        成功数量 = 0
        失败数量 = 0
        失败列表 = []

        for auth_code in auth_codes:
            try:
                响应 = 发送管理员请求({
                    'action': 'set_cloud_data',
                    'auth_code': auth_code,
                    'cloud_data': '会员'
                })

                if 响应.get('status') == 'success':
                    成功数量 += 1
                    记录日志("INFO", "批量登记会员成功", {"激活码": auth_code})
                else:
                    失败数量 += 1
                    失败原因 = 响应.get('message', '未知错误')
                    失败列表.append(f"{auth_code}: {失败原因}")
                    记录日志("ERROR", "批量登记会员失败", {"激活码": auth_code, "错误": 失败原因})

            except Exception as e:
                失败数量 += 1
                失败列表.append(f"{auth_code}: {str(e)}")
                记录日志("ERROR", "批量登记会员异常", {"激活码": auth_code, "错误": str(e)})

        结果消息 = f"批量登记会员完成: 成功{成功数量}个, 失败{失败数量}个"
        if 失败列表:
            结果消息 += f"\n失败详情: {'; '.join(失败列表[:5])}"
            if len(失败列表) > 5:
                结果消息 += f"等{len(失败列表)}个失败"

        return jsonify({
            'status': 'success' if 成功数量 > 0 else 'error',
            'message': 结果消息,
            'success_count': 成功数量,
            'failed_count': 失败数量
        })

    except Exception as e:
        记录日志("ERROR", "批量登记会员异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量登记会员失败: {str(e)}'
        })

@app.route('/api/batch_set_cloud_data', methods=['POST'])
@需要CSRF验证
def 批量设置云数据API():
    """批量设置云数据API"""
    try:
        data = request.get_json()
        auth_codes = data.get('auth_codes', [])
        cloud_data = data.get('cloud_data', '')

        if not auth_codes:
            return jsonify({
                'status': 'error',
                'message': '请选择要设置的激活码'
            })

        成功数量 = 0
        失败数量 = 0
        失败列表 = []

        for auth_code in auth_codes:
            try:
                响应 = 发送管理员请求({
                    'action': 'set_cloud_data',
                    'auth_code': auth_code,
                    'cloud_data': cloud_data
                })

                if 响应.get('status') == 'success':
                    成功数量 += 1
                    记录日志("INFO", "批量设置云数据成功", {"激活码": auth_code, "数据长度": len(cloud_data)})
                else:
                    失败数量 += 1
                    失败原因 = 响应.get('message', '未知错误')
                    失败列表.append(f"{auth_code}: {失败原因}")
                    记录日志("ERROR", "批量设置云数据失败", {"激活码": auth_code, "错误": 失败原因})

            except Exception as e:
                失败数量 += 1
                失败列表.append(f"{auth_code}: {str(e)}")
                记录日志("ERROR", "批量设置云数据异常", {"激活码": auth_code, "错误": str(e)})

        结果消息 = f"批量设置云数据完成: 成功{成功数量}个, 失败{失败数量}个"
        if 失败列表:
            结果消息 += f"\n失败详情: {'; '.join(失败列表[:5])}"
            if len(失败列表) > 5:
                结果消息 += f"等{len(失败列表)}个失败"

        return jsonify({
            'status': 'success' if 成功数量 > 0 else 'error',
            'message': 结果消息,
            'success_count': 成功数量,
            'failed_count': 失败数量
        })

    except Exception as e:
        记录日志("ERROR", "批量设置云数据异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量设置云数据失败: {str(e)}'
        })

@app.route('/api/batch_unban', methods=['POST'])
@需要CSRF验证
def 批量解封API():
    """批量解封API"""
    try:
        data = request.get_json()
        ban_list = data.get('ban_list', [])

        if not ban_list:
            return jsonify({
                'status': 'error',
                'message': '请选择要解封的项目'
            })

        成功数量 = 0
        失败数量 = 0
        失败列表 = []

        for ban_item in ban_list:
            try:
                ban_type = ban_item.get('ban_type')
                ban_value = ban_item.get('ban_value')

                响应 = 发送管理员请求({
                    'action': 'unban_target',
                    'ban_type': ban_type,
                    'ban_value': ban_value
                })

                if 响应.get('status') == 'success':
                    成功数量 += 1
                    记录日志("INFO", "批量解封成功", {"类型": ban_type, "值": ban_value})
                else:
                    失败数量 += 1
                    失败原因 = 响应.get('message', '未知错误')
                    失败列表.append(f"{ban_type}:{ban_value} - {失败原因}")
                    记录日志("ERROR", "批量解封失败", {"类型": ban_type, "值": ban_value, "错误": 失败原因})

            except Exception as e:
                失败数量 += 1
                失败列表.append(f"{ban_item} - {str(e)}")
                记录日志("ERROR", "批量解封异常", {"项目": ban_item, "错误": str(e)})

        结果消息 = f"批量解封完成: 成功{成功数量}个, 失败{失败数量}个"
        if 失败列表:
            结果消息 += f"\n失败详情: {'; '.join(失败列表[:3])}"
            if len(失败列表) > 3:
                结果消息 += f"等{len(失败列表)}个失败"

        return jsonify({
            'status': 'success' if 成功数量 > 0 else 'error',
            'message': 结果消息,
            'success_count': 成功数量,
            'failed_count': 失败数量
        })

    except Exception as e:
        记录日志("ERROR", "批量解封异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量解封失败: {str(e)}'
        })

@app.route('/api/delete_auth_code', methods=['POST'])
@需要CSRF验证
def 删除激活码API():
    """删除激活码API"""
    try:
        data = request.get_json()
        auth_code = data.get('auth_code', '').strip()

        if not auth_code:
            return jsonify({
                'status': 'error',
                'message': '激活码不能为空'
            })

        记录日志("INFO", "开始删除激活码", {"激活码": auth_code})

        响应 = 发送管理员请求({
            'action': 'delete_auth_code',
            'auth_code': auth_code
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "删除激活码成功", {"激活码": auth_code})
            return jsonify({
                'status': 'success',
                'message': f'激活码 {auth_code} 删除成功'
            })
        else:
            错误消息 = 响应.get('message', '删除激活码失败')
            记录日志("ERROR", "删除激活码失败", {"激活码": auth_code, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': f'删除激活码失败: {错误消息}'
            })

    except Exception as e:
        记录日志("ERROR", "删除激活码异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'删除激活码失败: {str(e)}'
        })

@app.route('/api/batch_delete_auth_codes', methods=['POST'])
@需要CSRF验证
def 批量删除激活码API():
    """批量删除激活码API"""
    try:
        data = request.get_json()
        auth_codes = data.get('auth_codes', [])

        if not auth_codes:
            return jsonify({
                'status': 'error',
                'message': '激活码列表不能为空'
            })

        if len(auth_codes) > 1000:
            return jsonify({
                'status': 'error',
                'message': '最多只能删除1000个激活码'
            })

        记录日志("INFO", "开始批量删除激活码", {
            "数量": len(auth_codes),
            "激活码列表": auth_codes[:10]  # 只记录前10个
        })

        响应 = 发送管理员请求({
            'action': 'batch_delete_auth_codes',
            'auth_codes': auth_codes
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "批量删除激活码完成", {
                "成功数量": 响应.get('data', {}).get('success_count', 0),
                "失败数量": 响应.get('data', {}).get('failed_count', 0)
            })
            return jsonify(响应)
        else:
            错误消息 = 响应.get('message', '批量删除激活码失败')
            记录日志("ERROR", "批量删除激活码失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': f'批量删除激活码失败: {错误消息}'
            })

    except Exception as e:
        记录日志("ERROR", "批量删除激活码异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'批量删除激活码失败: {str(e)}'
        })

# 日志分析API
@app.route('/api/logs', methods=['GET'])
@需要登录
def 获取日志列表API():
    """获取日志列表API"""
    try:
        # 获取筛选参数
        level = request.args.get('level', '')
        time_range = request.args.get('time_range', '')
        ip_filter = request.args.get('ip', '')
        keyword = request.args.get('keyword', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 100))

        记录日志("INFO", "获取日志列表请求", {
            "级别": level, "时间范围": time_range, "IP": ip_filter,
            "关键词": keyword, "页码": page, "限制": limit
        })

        # 构建请求数据
        请求数据 = {
            'action': 'get_logs',
            'level': level,
            'time_range': time_range,
            'ip': ip_filter,
            'keyword': keyword,
            'page': page,
            'limit': limit
        }

        # 发送请求到服务器获取日志
        响应 = 发送管理员请求(请求数据)

        if 响应 and 响应.get('status') == 'success':
            记录日志("INFO", "获取日志列表成功", {"条数": len(响应.get('data', []))})
            return jsonify(响应)
        else:
            错误信息 = 响应.get('message', '获取日志列表失败') if 响应 else '网络请求失败'
            记录日志("ERROR", "获取日志列表异常", {"错误": 错误信息})
            return jsonify({
                'status': 'error',
                'message': 错误信息
            })

    except Exception as e:
        记录日志("ERROR", "获取日志列表异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取日志列表失败: {str(e)}'
        })

@app.route('/api/logs/stats', methods=['GET'])
@需要登录
def 获取日志统计API():
    """获取日志统计API"""
    try:
        记录日志("INFO", "获取日志统计请求")

        # 发送请求到服务器获取日志统计
        响应 = 发送管理员请求({'action': 'get_log_stats'})

        if 响应 and 响应.get('status') == 'success':
            记录日志("INFO", "获取日志统计成功")
            return jsonify(响应)
        else:
            错误信息 = 响应.get('message', '获取日志统计失败') if 响应 else '网络请求失败'
            记录日志("ERROR", "获取日志统计失败", {"错误": 错误信息})
            return jsonify({
                'status': 'error',
                'message': 错误信息
            })

    except Exception as e:
        记录日志("ERROR", "获取日志统计异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取日志统计失败: {str(e)}'
        })

@app.route('/api/logs/export', methods=['GET'])
@需要登录
def 导出日志API():
    """导出日志API"""
    try:
        # 获取筛选参数
        level = request.args.get('level', '')
        time_range = request.args.get('time_range', '')
        ip_filter = request.args.get('ip', '')
        keyword = request.args.get('keyword', '')

        记录日志("INFO", "导出日志请求", {
            "级别": level, "时间范围": time_range, "IP": ip_filter, "关键词": keyword
        })

        # 构建请求数据
        请求数据 = {
            'action': 'export_logs',
            'level': level,
            'time_range': time_range,
            'ip': ip_filter,
            'keyword': keyword
        }

        # 发送请求到服务器导出日志
        响应 = 发送管理员请求(请求数据)

        if 响应 and 响应.get('status') == 'success':
            # 获取导出内容
            导出内容 = 响应.get('content', '')
            文件名 = 响应.get('filename', f'logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt')

            记录日志("INFO", "导出日志成功", {"文件名": 文件名, "条数": 响应.get('count', 0)})
            return 导出内容, 200, {
                'Content-Type': 'text/plain; charset=utf-8',
                'Content-Disposition': f'attachment; filename="{文件名}"'
            }
        else:
            错误信息 = 响应.get('message', '导出日志失败') if 响应 else '网络请求失败'
            记录日志("ERROR", "导出日志失败", {"错误": 错误信息})
            return jsonify({
                'status': 'error',
                'message': 错误信息
            })

    except Exception as e:
        记录日志("ERROR", "导出日志异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'导出日志失败: {str(e)}'
        })

@app.route('/api/logs/cleanup', methods=['POST'])
@需要登录
@需要CSRF验证
def 清理日志API():
    """清理旧日志API"""
    try:
        data = request.get_json()
        days = data.get('days', 30)  # 默认清理30天前的日志

        记录日志("INFO", "清理日志请求", {"天数": days})

        # 构建请求数据
        请求数据 = {
            'action': 'cleanup_logs',
            'days': days
        }

        # 发送请求到服务器清理日志
        响应 = 发送管理员请求(请求数据)

        if 响应 and 响应.get('status') == 'success':
            记录日志("INFO", "清理日志成功", {"清理数量": 响应.get('cleaned_count', 0)})
            return jsonify(响应)
        else:
            错误信息 = 响应.get('message', '清理日志失败') if 响应 else '网络请求失败'
            记录日志("ERROR", "清理日志失败", {"错误": 错误信息})
            return jsonify({
                'status': 'error',
                'message': 错误信息
            })

    except Exception as e:
        记录日志("ERROR", "清理日志异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'清理日志失败: {str(e)}'
        })

@app.route('/api/system_config', methods=['GET'])
@需要登录
def 获取系统配置API():
    """获取系统配置"""
    try:
        记录日志("INFO", "获取系统配置请求")

        响应 = 发送管理员请求({
            'action': 'get_system_config'
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "获取系统配置成功")
            return jsonify(响应)
        else:
            错误消息 = 响应.get('message', '获取系统配置失败')
            记录日志("ERROR", "获取系统配置失败", {"错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': f'获取系统配置失败: {错误消息}'
            })

    except Exception as e:
        记录日志("ERROR", "获取系统配置异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'获取系统配置失败: {str(e)}'
        })

@app.route('/api/update_system_config', methods=['POST'])
@需要登录
@需要CSRF验证
def 更新系统配置API():
    """更新系统配置"""
    try:
        数据 = request.get_json()
        配置键 = 数据.get('config_key', '')
        配置值 = 数据.get('config_value', '')

        记录日志("INFO", "更新系统配置请求", {"配置键": 配置键, "配置值": 配置值})

        if not 配置键:
            return jsonify({
                'status': 'error',
                'message': '配置键不能为空'
            })

        响应 = 发送管理员请求({
            'action': 'update_system_config',
            'config_key': 配置键,
            'config_value': 配置值
        })

        if 响应.get('status') == 'success':
            记录日志("INFO", "更新系统配置成功", {"配置键": 配置键, "配置值": 配置值})
            return jsonify(响应)
        else:
            错误消息 = 响应.get('message', '更新系统配置失败')
            记录日志("ERROR", "更新系统配置失败", {"配置键": 配置键, "错误": 错误消息})
            return jsonify({
                'status': 'error',
                'message': f'更新系统配置失败: {错误消息}'
            })

    except Exception as e:
        记录日志("ERROR", "更新系统配置异常", {"错误": str(e)})
        return jsonify({
            'status': 'error',
            'message': f'更新系统配置失败: {str(e)}'
        })

if __name__ == '__main__':
    记录日志("INFO", "Web管理端启动", {"服务器地址": 服务器地址})
    print("=" * 60)
    print("网络验证系统 - 超级管理员端 (Web版)")
    print("=" * 60)
    print(f"服务器地址: {服务器地址}")
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    print("访问地址: http://localhost:5000")
    print("管理员密码: admin123456")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
